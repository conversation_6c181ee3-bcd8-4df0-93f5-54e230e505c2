%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &68240917156290300
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5364558910336826677}
  - component: {fileID: 1166284884117228971}
  - component: {fileID: 2302858560441418422}
  - component: {fileID: 3791996394651089356}
  - component: {fileID: 8669866708047966680}
  - component: {fileID: 5547945960407703614}
  - component: {fileID: 2297187311713938533}
  - component: {fileID: 4265241822150746317}
  m_Layer: 0
  m_Name: "\u5F00\u95E8\u6761\u4EF6\u68C0\u6D4B"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5364558910336826677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -797.609, y: 11.74, z: 2263.1367}
  m_LocalScale: {x: 11.244347, y: 3.3, z: 7.9467497}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2211215281743277874}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1166284884117228971
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2302858560441418422
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3791996394651089356
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &8669866708047966680
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 11
  useButton: 1
  useCustomButtonName: 1
  customButtonName: "\u6253\u5F00"
  interaction_Base:
  - {fileID: 5547945960407703614}
--- !u!114 &5547945960407703614
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3ee50e58c700c9408bbd9d238cb6706, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  item: {fileID: 11400000, guid: 117bb8f4a4e4a7048962598ae5d9caa0, type: 2}
  checkhave: 1
  checkcount: 0
  count: 1
  OperatorIndex: 
  True:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7251445242669946967}
        m_TargetAssemblyTypeName: DG.Tweening.DOTweenAnimation, Assembly-CSharp-firstpass
        m_MethodName: DOPlay
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3637394639856523316}
        m_TargetAssemblyTypeName: DG.Tweening.DOTweenAnimation, Assembly-CSharp-firstpass
        m_MethodName: DOPlay
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 4265241822150746317}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 68240917156290300}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  False:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2297187311713938533}
        m_TargetAssemblyTypeName: Temp_SimpleAttention, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &2297187311713938533
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bd8cc6730391e0d44b62922b568a0cde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  simpleLog: "\u5730\u7262\u5927\u95E8\u5DF2\u4E0A\u9501"
  translate: 1
  localKey: "\u8981\u585E\u63D0\u793A7"
--- !u!114 &4265241822150746317
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 68240917156290300}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84a67533a243a244e8191abfb37ef0d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SaveName: "\u5730\u7262\u5927\u95E8\u5F00\u95E8"
  StateA:
  - gameObject: {fileID: 5764908023647730507}
    Active: 1
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 1
    rotation: {x: 0, y: -0.42261827, z: 0, w: 0.90630776}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  - gameObject: {fileID: 5764815901993739419}
    Active: 1
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 1
    rotation: {x: 0, y: 0.42261827, z: 0, w: 0.90630776}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  - gameObject: {fileID: 68240917156290300}
    Active: 0
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 0
    rotation: {x: 0, y: 0, z: 0, w: 0}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  StateB: []
  ChangeDirect: 1
--- !u!1 &6601508660693777056
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2211215281743277874}
  m_Layer: 0
  m_Name: "\u8981\u585E\u5730\u7262\u5F00\u95E8\u5224\u65AD"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2211215281743277874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6601508660693777056}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9094766367317340590}
  - {fileID: 5364558910336826677}
  - {fileID: 5767459689999705231}
  m_Father: {fileID: 0}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1221289354764948496
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2211215281743277874}
    m_Modifications:
    - target: {fileID: 5205441082756262205, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_Name
      value: SM_Portcullis
      objectReference: {fileID: 0}
    - target: {fileID: 5205441082756262205, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5205441082756262205, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483647
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 2.56066
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 3.1999998
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 2.56066
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -797.58
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 24.29
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2265.7
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.000000021855694
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4, type: 3}
--- !u!4 &9094766367317340590 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7982065060105528766, guid: 5d3f4da57a22d1d43a8e8cc7543b62e4,
    type: 3}
  m_PrefabInstance: {fileID: 1221289354764948496}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5766109748534445277
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2211215281743277874}
    m_Modifications:
    - target: {fileID: 1526675336709840, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_Name
      value: Door_03
      objectReference: {fileID: 0}
    - target: {fileID: 1526675336709840, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalScale.x
      value: 3.5
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalScale.y
      value: 3.3
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalScale.z
      value: 3.5
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalPosition.x
      value: -797.42
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalPosition.y
      value: 9.79
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2265.36
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.097569
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.686
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.26715958
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4999438862400030, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4999438862400030, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4999438862400030, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4999438862400030, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4999438862400030, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 1204007662157718, guid: 6bdc2cef59d54d86ac31b264c1247713,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 7251445242669946967}
    - targetCorrespondingSourceObject: {fileID: 1666589590822982, guid: 6bdc2cef59d54d86ac31b264c1247713,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 3637394639856523316}
  m_SourcePrefab: {fileID: 100100000, guid: 6bdc2cef59d54d86ac31b264c1247713, type: 3}
--- !u!1 &5764815901993739419 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1666589590822982, guid: 6bdc2cef59d54d86ac31b264c1247713,
    type: 3}
  m_PrefabInstance: {fileID: 5766109748534445277}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &3637394639856523316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5764815901993739419}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d0390bd8b8ffd640b34fe25065ff1df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  updateType: 0
  isSpeedBased: 0
  hasOnStart: 0
  hasOnPlay: 0
  hasOnUpdate: 0
  hasOnStepComplete: 0
  hasOnComplete: 0
  hasOnTweenCreated: 0
  hasOnRewind: 0
  onStart:
    m_PersistentCalls:
      m_Calls: []
  onPlay:
    m_PersistentCalls:
      m_Calls: []
  onUpdate:
    m_PersistentCalls:
      m_Calls: []
  onStepComplete:
    m_PersistentCalls:
      m_Calls: []
  onComplete:
    m_PersistentCalls:
      m_Calls: []
  onTweenCreated:
    m_PersistentCalls:
      m_Calls: []
  onRewind:
    m_PersistentCalls:
      m_Calls: []
  targetIsSelf: 1
  targetGO: {fileID: 0}
  tweenTargetIsTargetGO: 1
  delay: 0
  duration: 2
  easeType: 6
  easeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  loopType: 0
  loops: 1
  id: 
  isRelative: 0
  isFrom: 0
  isIndependentUpdate: 0
  autoKill: 0
  autoGenerate: 1
  isActive: 1
  isValid: 1
  target: {fileID: 5770400517748965059}
  animationType: 3
  targetType: 11
  forcedTargetType: 0
  autoPlay: 0
  useTargetAsV3: 0
  endValueFloat: 0
  endValueV3: {x: 0, y: 50, z: 0}
  endValueV2: {x: 0, y: 0}
  endValueColor: {r: 1, g: 1, b: 1, a: 1}
  endValueString: 
  endValueRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 0
    height: 0
  endValueTransform: {fileID: 0}
  optionalBool0: 0
  optionalBool1: 0
  optionalFloat0: 0
  optionalInt0: 0
  optionalRotationMode: 0
  optionalScrambleMode: 0
  optionalShakeRandomnessMode: 0
  optionalString: 
--- !u!1 &5764908023647730507 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1204007662157718, guid: 6bdc2cef59d54d86ac31b264c1247713,
    type: 3}
  m_PrefabInstance: {fileID: 5766109748534445277}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7251445242669946967
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5764908023647730507}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d0390bd8b8ffd640b34fe25065ff1df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  updateType: 0
  isSpeedBased: 0
  hasOnStart: 0
  hasOnPlay: 0
  hasOnUpdate: 0
  hasOnStepComplete: 0
  hasOnComplete: 0
  hasOnTweenCreated: 0
  hasOnRewind: 0
  onStart:
    m_PersistentCalls:
      m_Calls: []
  onPlay:
    m_PersistentCalls:
      m_Calls: []
  onUpdate:
    m_PersistentCalls:
      m_Calls: []
  onStepComplete:
    m_PersistentCalls:
      m_Calls: []
  onComplete:
    m_PersistentCalls:
      m_Calls: []
  onTweenCreated:
    m_PersistentCalls:
      m_Calls: []
  onRewind:
    m_PersistentCalls:
      m_Calls: []
  targetIsSelf: 1
  targetGO: {fileID: 0}
  tweenTargetIsTargetGO: 1
  delay: 0
  duration: 2
  easeType: 6
  easeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  loopType: 0
  loops: 1
  id: 
  isRelative: 0
  isFrom: 0
  isIndependentUpdate: 0
  autoKill: 0
  autoGenerate: 1
  isActive: 1
  isValid: 1
  target: {fileID: 5770466782292008423}
  animationType: 3
  targetType: 11
  forcedTargetType: 0
  autoPlay: 0
  useTargetAsV3: 0
  endValueFloat: 0
  endValueV3: {x: 0, y: -50, z: 0}
  endValueV2: {x: 0, y: 0}
  endValueColor: {r: 1, g: 1, b: 1, a: 1}
  endValueString: 
  endValueRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 0
    height: 0
  endValueTransform: {fileID: 0}
  optionalBool0: 0
  optionalBool1: 0
  optionalFloat0: 0
  optionalInt0: 0
  optionalRotationMode: 0
  optionalScrambleMode: 0
  optionalShakeRandomnessMode: 0
  optionalString: 
--- !u!4 &5767459689999705231 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4349960048943186, guid: 6bdc2cef59d54d86ac31b264c1247713,
    type: 3}
  m_PrefabInstance: {fileID: 5766109748534445277}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5770400517748965059 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4999438862400030, guid: 6bdc2cef59d54d86ac31b264c1247713,
    type: 3}
  m_PrefabInstance: {fileID: 5766109748534445277}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5770466782292008423 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4933178966202682, guid: 6bdc2cef59d54d86ac31b264c1247713,
    type: 3}
  m_PrefabInstance: {fileID: 5766109748534445277}
  m_PrefabAsset: {fileID: 0}
