# AI 本地化翻译工具策划案

## 1. 目标

开发一个 Unity 编辑器工具，利用可配置的 AI 翻译服务（以 DeepSeek API 为例），自动将 `BaseLocalization.csv` 文件中的源文本翻译成指定的目标语言，并生成或更新对应的语言 CSV 文件（如 `en.csv`, `jp.csv`），存放于运行时加载的路径 (`Assets/StreamingAssets/Localization/`)。

## 2. 核心功能

*   读取 `Assets/GameData/Localization/BaseLocalization.csv` 作为翻译源。
*   允许用户配置 AI 翻译服务的 API Key 和 API Base URL。
*   允许用户指定一个或多个目标语言代码（例如 "en", "jp", "fr"）。
*   读取已存在的目标语言 CSV 文件（如 `Assets/StreamingAssets/Localization/en.csv`），以识别已翻译的 Key，避免重复翻译（可选，可配置）。
*   将待翻译的文本**分批次**发送给 AI 翻译服务。
*   解析 AI 返回的翻译结果。
*   将翻译结果写入到 `Assets/StreamingAssets/Localization/` 目录下对应的 `[语言代码].csv` 文件中。
*   提供清晰的用户界面和操作反馈。

## 3. 用户界面 (Unity Editor Window)

创建一个自定义编辑器窗口 (`AITranslationWindow.cs`)：

*   **API Endpoint (Base URL):** 文本输入字段，用于 AI 服务的 Base URL (例如: `https://api.deepseek.com`)。
*   **API Key:** 文本输入字段（建议使用密码字段或提示用户安全存储），用于 AI 服务的 API Key。
*   **目标语言 (Target Languages):**
    *   一个可编辑的列表或文本区域，允许用户输入目标语言代码（如 "en", "jp", "de"），用逗号或换行分隔。
    *   (可选) 一个按钮，用于扫描 `Assets/StreamingAssets/Localization/` 目录，自动填充已存在的语言文件代码。
*   **批量大小 (Batch Size):** 整数输入字段，指定每次 API 请求包含的翻译条目数量（默认 20）。
*   **翻译模式 (Translation Mode):**
    *   **覆盖 (Overwrite):** (默认) 总是用新的翻译结果完全覆盖目标语言文件。
    *   **仅新增 (Add Only):** 只翻译源 CSV 中存在但目标 CSV 中不存在的 Key。保留目标 CSV 中已有的翻译。
    *   **更新 (Update):** （较复杂，待定）翻译源 CSV 中存在但目标 CSV 中不存在的 Key，并重新翻译那些源文本在 `BaseLocalization.csv` 中有更新的 Key。
*   **操作按钮:**
    *   **开始翻译 (Start Translation):** 触发翻译流程。
*   **状态显示:**
    *   文本区域，用于显示当前操作状态、进度信息、错误日志等。
    *   (可选) 进度条，显示总体翻译进度。

## 4. 工作流程

1.  **用户打开窗口:** 通过 Unity 菜单项打开 "AI Translation Tool" 窗口。
2.  **用户配置:** 用户输入 API Endpoint, API Key, 目标语言代码, 并选择翻译模式和批量大小。
3.  **点击 "开始翻译":**
    *   工具验证输入（API Key/URL 是否为空，语言代码是否有效）。
    *   **读取 BaseLocalization.csv:** 加载 `Assets/GameData/Localization/BaseLocalization.csv` 到 `Dictionary<string, string> baseData`。
    *   **遍历目标语言:** 对用户指定的每个目标语言代码 `langCode` 执行以下步骤：
        *   **确定目标文件路径:** `targetCsvPath = Path.Combine(Application.streamingAssetsPath, "Localization", $"{langCode}.csv")`。
        *   **读取目标 CSV (如果需要):** 根据选择的"翻译模式"，可能需要读取现有的 `targetCsvPath` 文件到 `Dictionary<string, string> existingTargetData`。
        *   **识别待翻译条目:**
            *   初始化 `List<KeyValuePair<string, string>> itemsToTranslate`。
            *   遍历 `baseData`：
                *   获取 `key` 和 `sourceValue`。
                *   根据"翻译模式"判断是否需要翻译此 `key`:
                    *   **覆盖模式:** 所有 `key` 都需要翻译。
                    *   **仅新增模式:** 只有当 `!existingTargetData.ContainsKey(key)` 时才需要翻译。
                    *   **(忽略更新模式)**
                *   如果需要翻译，将 `{key, sourceValue}` 添加到 `itemsToTranslate`。
        *   **分批处理:** 将 `itemsToTranslate` 分成多个批次（每批大小由用户设定）。
        *   **执行翻译批次:** 对每个批次：
            *   准备 API 请求数据。
            *   调用 AI 翻译接口 (详见第 5 节)。
            *   处理 API 响应，将翻译结果存入临时的 `Dictionary<string, string> translatedData`。
            *   更新 UI 显示进度。
            *   处理 API 调用错误（重试？跳过？）。
        *   **合并/写入目标 CSV:**
            *   根据"翻译模式"准备最终要写入的数据 `finalTargetData`：
                *   **覆盖模式:** `finalTargetData = translatedData`。
                *   **仅新增模式:** `finalTargetData = existingTargetData`，然后将 `translatedData` 中的内容合并进去。
            *   确保 `Assets/StreamingAssets/Localization/` 目录存在。
            *   使用 `CsvHelper` 将 `finalTargetData` 写入 `targetCsvPath` (UTF-8 编码)。
        *   **刷新 AssetDatabase:** 调用 `AssetDatabase.Refresh()` 让 Unity 编辑器识别文件更改。
4.  **完成:** 在状态区域显示完成信息或错误总结。

## 5. AI 接口交互

*   **请求构建:**
    *   使用 `UnityEngine.Networking.UnityWebRequest` 发送 POST 请求到用户配置的 API Endpoint。
    *   设置 `Authorization: Bearer [API_KEY]` Header。
    *   设置 `Content-Type: application/json`。
    *   **请求体 (JSON):** 需要根据目标 AI 服务的要求构建。参考 DeepSeek 示例，可能类似：
      ```json
      {
        "model": "deepseek-chat", // 或其他模型
        "messages": [
          {"role": "system", "content": "You are a professional translator. Translate the user's text snippets into [Target Language Name, e.g., Japanese]. Respond ONLY with the translations, maintaining the original order and number of snippets. If multiple snippets are provided, separate translations with a unique delimiter like '|||'."},
          {"role": "user", "content": "[Snippet1]|||[Snippet2]|||[Snippet3]..."} // 发送拼接好的待翻译文本
        ],
        "stream": false
      }
      ```
    *   **System Prompt:** 需要精心设计，明确指示 AI 进行翻译，指定目标语言，并要求按特定格式返回结果（例如用特定分隔符 `|||` 分隔多条翻译）。
    *   **User Content:** 将批次内的源文本用分隔符拼接起来。
*   **响应处理:**
    *   解析返回的 JSON 数据。
    *   提取 `choices[0].message.content` 中的翻译结果字符串。
    *   使用分隔符 `|||` 将结果字符串拆分回对应批次的翻译文本。
    *   将翻译后的文本与批次中的 Key 关联起来。
*   **错误处理:**
    *   处理网络错误 (Timeout, Connection Error)。
    *   处理 HTTP 错误 (4xx - 客户端错误如无效 Key，5xx - 服务器错误)。
    *   处理 API 特定错误 (解析 JSON 返回的错误信息，如速率限制)。
    *   提供重试机制或跳过失败批次的选项。

## 6. CSV 处理

*   **读取:** 使用 `CsvHelper` 读取 `BaseLocalization.csv` 和现有的目标语言 CSV。指定 UTF-8 编码。
*   **写入:** 使用 `CsvHelper` 将翻译结果写入目标语言 CSV 文件 (`[langCode].csv`)。确保使用 UTF-8 编码，并包含 "Key", "Value" 表头。文件应保存在 `Assets/StreamingAssets/Localization/` 目录下。

## 7. 配置

*   API Key 和 Endpoint 应存储在 `EditorPrefs` 中，以便在 Unity 编辑器会话之间保留。**警告:** 直接存储 API Key 有安全风险，建议提示用户或查找更安全的存储方案（如环境变量、配置文件等，但这超出了简单工具的范畴）。
*   目标语言列表和批量大小也存储在 `EditorPrefs` 中。

## 8. 错误处理

*   **文件未找到:** `BaseLocalization.csv` 不存在时报错。
*   **CSV 格式错误:** 读取 CSV 时处理异常。
*   **API 认证失败:** 提示检查 API Key。
*   **API 请求错误:** 显示 API 返回的错误信息。
*   **网络问题:** 提示检查网络连接。
*   **速率限制:** 提示用户减小批量大小或等待。

## 9. 输出路径

*   所有翻译生成的目标语言 CSV 文件 (`en.csv`, `jp.csv` 等) 都应直接写入 `Assets/StreamingAssets/Localization/` 目录。

## 10. 注意事项 / 待办事项

*   **API Key 安全:** 需要强调 API Key 的安全存储问题。
*   **System Prompt 优化:** 需要测试和优化 System Prompt 以获得最佳翻译质量和格式。
*   **目标语言名称:** System Prompt 需要目标语言的准确名称（如 "Japanese" 而不是 "jp"），可能需要一个映射关系。
*   **特殊字符处理:** 确保文本中的换行符、引号等在发送给 API 和写回 CSV 时得到正确处理。
*   **成本考虑:** 提醒用户 AI翻译 API 调用通常是收费的。
*   **"更新" 模式:** 实现更复杂的更新逻辑，需要对比 `BaseLocalization.csv` 中的 Value 是否有变化。
*   **异步编辑器窗口:** 对于长时间运行的任务，考虑使用异步操作更新 UI，防止编辑器卡死。

## 11. 实现总结

本地化翻译工具已成功实现，具备以下功能：

*   使用DeepSeek API自动将BaseLocalization.csv中的源文本翻译为多种目标语言
*   支持批量翻译处理，可配置批次大小、目标语言和翻译模式
*   将翻译结果存储为标准CSV格式，保存到StreamingAssets/Localization/目录下
*   提供清晰的编辑器界面，方便用户配置API参数和监控翻译进度
*   完整的错误处理机制，包括API错误、网络问题和翻译异常处理
*   详细的翻译日志和进度报告

工具已成功用于生成en.csv（英文）和jp.csv（日文）翻译文件，为游戏提供了基础的多语言支持。结合LocalizationManager运行时系统，可实现完整的游戏本地化功能。
