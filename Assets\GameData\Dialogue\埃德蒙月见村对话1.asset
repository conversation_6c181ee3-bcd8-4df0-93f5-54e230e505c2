%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9022363247168752023
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": e799c8e277bb7fb449b3b8fb19123512
  "\u8282\u70B9\u5750\u6807": {x: 3067.9995, y: 1399}
  "\u5B50\u8282\u70B9":
  - {fileID: 2024434682042387104}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5979\u5F88\u65E9\u5C31\u79BB\u5F00\u5BB6\u65CF\uFF0C\u72EC\u81EA\u51FA\u53BB\u65C5\u884C\u4E86\u3002\u5979\u8BF4\u6BD4\u8D77\u6210\u4E3A\u6E29\u5BA4\u91CC\u7684\u4E00\u6735\u82B1\uFF0C\u5979\u66F4\u60F3\u505A\u7FF1\u7FD4\u5929\u7A7A\u7684\u4E00\u53EA\u9E70\u2026\u2026\u5F88\u5E05\u6C14\u554A\u3002"
--- !u!114 &-8773142867330805837
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 6c7f766b74d594849a6d3d161e57b452
  "\u8282\u70B9\u5750\u6807": {x: 2166, y: 313.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: 4432065160540165541}
  - {fileID: -5219564224489388893}
  - {fileID: -488067928631771679}
  - {fileID: -4305443923849872389}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-7859420478629939702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": e9a551ad8177c79469d493a232232b1e
  "\u8282\u70B9\u5750\u6807": {x: -74.350006, y: 1628.0002}
  "\u5B50\u8282\u70B9":
  - {fileID: 6216363080202810879}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5E10\u7BF7\u90A3\u8FB9\u6709\u6211\u521A\u716E\u597D\u7684\u6D53\u6C64\uFF0C\u53EF\u4EE5\u5148\u586B\u586B\u809A\u5B50\u3002"
--- !u!114 &-6678009319246713658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 050c1da8aa7a1e74a8e626df8afe51e4
  "\u8282\u70B9\u5750\u6807": {x: -102.99999, y: 1421.5409}
  "\u5B50\u8282\u70B9":
  - {fileID: -7859420478629939702}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6765\u5230\u8FD9\u91CC\u5E94\u8BE5\u5F88\u8F9B\u82E6\u5427\uFF1F\u4E0D\u8FC7\u4F60\u653E\u5FC3\uFF0C\u6708\u89C1\u6751\u5F88\u5B89\u5168\uFF0C\u53EF\u4EE5\u653E\u5FC3\u4F11\u606F\u3002"
--- !u!114 &-6453008029026679231
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 1176af92203dacb41b49a08662855cd4
  "\u8282\u70B9\u5750\u6807": {x: 794.99994, y: 801.99994}
  "\u5B50\u8282\u70B9":
  - {fileID: 8428387671464012926}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F60\u613F\u610F\u8BA9\u6211\u8BD5\u8BD5\u770B\u5417\uFF1F\u53EA\u9700\u8981\u63D0\u4F9B\u4E00\u4E9B\u953B\u9020\u77F3\u5C31\u53EF\u4EE5\u3002"
--- !u!114 &-5219564224489388893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5173\u4E8E\u57C3\u5FB7\u8499"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 1d19fbe80fe777a4a8ab77538027c851
  "\u8282\u70B9\u5750\u6807": {x: 2447, y: 577.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: 5866623553554761765}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u867D\u7136\u6211\u8BF4\u81EA\u5DF1\u662F\u4E00\u540D\u9A91\u58EB\uFF0C\u4F46\u5176\u5B9E\u6211\u8FD8\u5728\u89C1\u4E60\u9636\u6BB5\uFF0C\u79F0\u4E0D\u4E0A\u771F\u6B63\u7684\u9A91\u58EB\u2026\u2026"
--- !u!114 &-4938153764385865893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f386d821d88a4864e8fec269f23db756
  "\u8282\u70B9\u5750\u6807": {x: 2520.8152, y: 1407}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u662F\u8FD9\u4E48\u8BA4\u4E3A\u7684\u3002"
--- !u!114 &-4305443923849872389
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u79BB\u5F00"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 54117b006a28ca84b94ac5198ecd5277
  "\u8282\u70B9\u5750\u6807": {x: 3632.0002, y: 593.41974}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8FD9\u91CC\u5F88\u5B89\u5168\uFF0C\u4F60\u53EF\u4EE5\u5B89\u5FC3\u4F11\u606F\u3002"
--- !u!114 &-3757369247680473989
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 06a42dd88d4e1ac44a007c2d6c109c1a
  "\u8282\u70B9\u5750\u6807": {x: 794.9999, y: 584}
  "\u5B50\u8282\u70B9":
  - {fileID: -6453008029026679231}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6B63\u597D\u6211\u66FE\u5B66\u4E60\u8FC7\u6253\u78E8\u6B66\u5668\uFF0C\u867D\u7136\u53EA\u662F\u4E9B\u57FA\u7840\uFF0C\u4F46\u5E94\u8BE5\u4E5F\u80FD\u5E2E\u4E0A\u70B9\u5FD9\u3002"
--- !u!114 &-2974573649389346335
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 9335de9ef98711844a59f9798a929647
  "\u8282\u70B9\u5750\u6807": {x: -155.78818, y: 436}
  "\u5B50\u8282\u70B9":
  - {fileID: 6056332120517694583}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u5C11\u5E74"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4ECE\u4F60\u8FDB\u5165\u6751\u5B50\u5F00\u59CB\u6211\u5C31\u5728\u89C2\u5BDF\uFF0C\u4F60\u770B\u8D77\u6765\u4E0D\u50CF\u65E0\u5FC3\u7684\u5931\u8272\u4EBA\uFF0C\u4E5F\u4E0D\u50CF\u6CA1\u6709\u7406\u667A\u7684\u602A\u517D\u2026\u2026\u6211\u60F3\uFF0C\u4F60\u5E94\u8BE5\u4E0D\u662F\u574F\u4EBA\u3002"
--- !u!114 &-2090358742743788978
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 270aee5408889de43b68dd6ffed91d5d
  "\u8282\u70B9\u5750\u6807": {x: 550.9999, y: 1620}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u53EF\u60DC\u4ED6\u53BB\u4E86\u68EE\u6797\u5BFB\u627E\u6750\u6599\u2026\u2026\u4F46\u613F\u5973\u795E\u4FDD\u4F51\uFF0C\u4ED6\u80FD\u5FEB\u4E9B\u56DE\u6765\u3002"
--- !u!114 &-1292942016525015846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 049a89dfb26f5c743a8b7ea14b63bc47
  "\u8282\u70B9\u5750\u6807": {x: -102.99997, y: 1215.2087}
  "\u5B50\u8282\u70B9":
  - {fileID: -6678009319246713658}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u521A\u521A\u6B63\u5728\u9644\u8FD1\u5DE1\u903B\uFF0C\u5C31\u770B\u5230\u4F60\u7A81\u7136\u51FA\u73B0\uFF0C\u6211\u8FD8\u4EE5\u4E3A\u662F\u5916\u9762\u90A3\u4E9B\u602A\u2026\u2026\u6211\u662F\u8BF4\uFF0C\u5165\u4FB5\u8005\uFF0C\u8FD8\u597D\u2026\u2026\u53EA\u662F\u865A\u60CA\u4E00\u573A\u3002"
--- !u!114 &-1017407810297934250
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": c0546f6cf2ddce040b89ba95c6f750bf
  "\u8282\u70B9\u5750\u6807": {x: -125.99999, y: 258.3384}
  "\u5B50\u8282\u70B9":
  - {fileID: -2974573649389346335}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u5C11\u5E74"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u80FD\u5426\u544A\u8BC9\u6211\u4F60\u7684\u8EAB\u4EFD\uFF1F\u5728\u786E\u8BA4\u4E4B\u524D\uFF0C\u6211\u4E0D\u80FD\u8BA9\u4F60\u79BB\u5F00\uFF08\u63E1\u7D27\u5251\u67C4\uFF09\u3002"
--- !u!114 &-488067928631771679
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5173\u4E8E\u6D53\u6C64"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": ea794dc6d09d61f4cb6d3edeba02a196
  "\u8282\u70B9\u5750\u6807": {x: 3028, y: 585.4198}
  "\u5B50\u8282\u70B9":
  - {fileID: 7438690791645675423}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8FD8\u8981\u518D\u6765\u70B9\u6D53\u6C64\u5417\uFF1F\u867D\u7136\u5B83\u7684\u989C\u8272\u770B\u4E0A\u53BB\u2026\u2026\u5514\uFF0C\u4E0D\u592A\u7F8E\u89C2\uFF0C\u4F46\u662F\u8425\u517B\u7EDD\u5BF9\u6CA1\u6709\u95EE\u9898\u3002"
--- !u!114 &-177730033958129444
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 86802f1a6a007a243809b4c0e154f413
  "\u8282\u70B9\u5750\u6807": {x: 1923.0001, y: 732.41974}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5B9A\u671F\u4FDD\u517B\u81EA\u5DF1\u7684\u6B66\u5668\u4E5F\u662F\u9A91\u58EB\u7684\u5FC5\u4FEE\u8BFE\u4E4B\u4E00\u5462\u3002"
--- !u!114 &-151604714131417213
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": d8f5a1d696d2cd84f9967beff4bb98ca
  "\u8282\u70B9\u5750\u6807": {x: 2455, y: 1004.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: 5910608102519280781}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5C3D\u7BA1\u6211\u5F88\u60F3\u5C3D\u5FEB\u627E\u5230\u5931\u6563\u7684\u961F\u53CB\uFF0C\u548C\u4ED6\u4EEC\u4E00\u540C\u8FD4\u56DE\u9996\u90FD\uFF0C\u4F46\u6211\u4E0D\u80FD\u5BF9\u8FD9\u91CC\u7684\u4EBA\u7F6E\u4E4B\u4E0D\u7406\u2026\u2026"
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 204e534958e91824783fa0a7dc10275e, type: 3}
  m_Name: "\u57C3\u5FB7\u8499\u6708\u89C1\u6751\u5BF9\u8BDD1"
  m_EditorClassIdentifier: 
  treeGuid: 3402a76ef90288948bbf779b47ce5683
  "\u6839\u8282\u70B9": {fileID: 4610325365902330898}
  "\u76EE\u524D\u8282\u70B9": {fileID: 4610325365902330898}
  "\u6811\u72B6\u6001": 1
  "\u8282\u70B9\u5217\u8868":
  - {fileID: 4610325365902330898}
  - {fileID: 5706061713931194760}
  - {fileID: -1017407810297934250}
  - {fileID: 6056332120517694583}
  - {fileID: 4907565669281974943}
  - {fileID: -1292942016525015846}
  - {fileID: 1502682075271037778}
  - {fileID: -8773142867330805837}
  - {fileID: 4432065160540165541}
  - {fileID: -5219564224489388893}
  - {fileID: 5866623553554761765}
  - {fileID: -151604714131417213}
  - {fileID: -177730033958129444}
  - {fileID: 5910608102519280781}
  - {fileID: -4938153764385865893}
  - {fileID: -488067928631771679}
  - {fileID: 7438690791645675423}
  - {fileID: 2703104476277605637}
  - {fileID: 5989715467250499380}
  - {fileID: -9022363247168752023}
  - {fileID: 2024434682042387104}
  - {fileID: 4658052750994071826}
  - {fileID: -4305443923849872389}
  - {fileID: 5057465199843733920}
  - {fileID: -6678009319246713658}
  - {fileID: 6216363080202810879}
  - {fileID: -3757369247680473989}
  - {fileID: 8428387671464012926}
  - {fileID: 7962311503985772671}
  - {fileID: 4878508347537005924}
  - {fileID: 193073000187554423}
  - {fileID: -2090358742743788978}
  - {fileID: 201943257693802927}
  - {fileID: -6453008029026679231}
  - {fileID: 7365370753255634302}
  - {fileID: -2974573649389346335}
  - {fileID: -7859420478629939702}
--- !u!114 &193073000187554423
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 50c8fb3f420d7b04c9c248eb2223a737
  "\u8282\u70B9\u5750\u6807": {x: 567.03577, y: 1422.7886}
  "\u5B50\u8282\u70B9":
  - {fileID: -2090358742743788978}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u53EA\u662F\u8FD9\u4E2A\u7A0B\u5EA6\u6211\u8FD8\u80FD\u5E94\u4ED8\uFF0C\u8981\u60F3\u8FDB\u884C\u66F4\u9AD8\u9636\u7684\u5F3A\u5316\uFF0C\u5C31\u53EA\u80FD\u62DC\u6258\u5FB7\u9C81\u59C6\u5927\u53D4\u4E86\u3002"
--- !u!114 &201943257693802927
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": cf4f06a2dc39a2945a6e651d0809ad0a
  "\u8282\u70B9\u5750\u6807": {x: 1105.0359, y: 1461.7886}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5982\u679C\u4F60\u6539\u53D8\u4E3B\u610F\u4E86\uFF0C\u8BB0\u5F97\u968F\u65F6\u6765\u627E\u6211\u3002"
--- !u!114 &1502682075271037778
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4dbf8c88b0405a74282149d57c8d3e4e
  "\u8282\u70B9\u5750\u6807": {x: 2158, y: 85.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: -8773142867330805837}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 1
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6709\u4EC0\u4E48\u6211\u80FD\u5E2E\u4E0A\u5FD9\u7684\u5417\uFF1F"
--- !u!114 &2024434682042387104
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 43c42a6f79a20da40935463d5910e3a0
  "\u8282\u70B9\u5750\u6807": {x: 3075.9998, y: 1607.9998}
  "\u5B50\u8282\u70B9":
  - {fileID: 4658052750994071826}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u3001\u6211\u662F\u8BF4\uFF0C\u6211\u5F88\u4F69\u670D\u59D0\u59D0\uFF0C\u5979\u8DB3\u4EE5\u8BA9\u4EBA\u5C0A\u656C\u3002"
--- !u!114 &2703104476277605637
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u59D0\u59D0\uFF1F"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": b032819192b2eb241af173b4618c098e
  "\u8282\u70B9\u5750\u6807": {x: 3122, y: 1182.4197}
  "\u5B50\u8282\u70B9":
  - {fileID: -9022363247168752023}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u59D0\u59D0\u662F\u4E00\u540D\u771F\u6B63\u7684\u9A91\u58EB\uFF0C\u65E0\u8BBA\u6B66\u827A\u3001\u54C1\u683C\u3001\u5934\u8111\uFF0C\u90FD\u8DB3\u4EE5\u62C5\u5F53\u9A91\u58EB\u7684\u79F0\u53F7\uFF0C\u9664\u4E86\u6709\u4E00\u70B9\u2026\u2026\u51B2\u52A8\u3002"
--- !u!114 &4432065160540165541
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c63c0b701f71d04bad40980ae3dd6dd, type: 3}
  m_Name: WeaponUpgradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u6253\u78E8\u6B66\u5668"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 804ae556c30b3be42946c4989170f96e
  "\u8282\u70B9\u5750\u6807": {x: 1991.0001, y: 542.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: -177730033958129444}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &4610325365902330898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f716134a78f37df418d8eb84a3f5229d, type: 3}
  m_Name: ConditionCheckSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": dd33d6649f0182b45b08944194688df0
  "\u8282\u70B9\u5750\u6807": {x: 699, y: -225.47218}
  "\u5B50\u8282\u70B9":
  - {fileID: 5706061713931194760}
  - {fileID: 1502682075271037778}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &4658052750994071826
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": ee5e239dc424b474885262ce226b1cca
  "\u8282\u70B9\u5750\u6807": {x: 3084, y: 1814.5408}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E0D\u8FC7\u5076\u5C14\u6211\u4E5F\u4F1A\u60F3\uFF0C\u8981\u662F\u5979\u80FD\u518D\u7A33\u91CD\u4E00\u4E9B\u5C31\u597D\u4E86\u2026\u2026\u81F3\u5C11\u51FA\u95E8\u7684\u65F6\u5019\u4E0D\u8981\u5FD8\u8BB0\u5E26\u4E0A\u5730\u56FE\u3002"
--- !u!114 &4878508347537005924
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u4E0D\u7528\u4E86"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": d6ce27a53ec628f4eb585da34a2bc09c
  "\u8282\u70B9\u5750\u6807": {x: 1113.0359, y: 1243.7886}
  "\u5B50\u8282\u70B9":
  - {fileID: 201943257693802927}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u73B0\u5728\u8FD8\u4E0D\u9700\u8981\u5417\uFF1F\u90A3\u597D\u5427\u2026\u2026"
--- !u!114 &4907565669281974943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 21786578353c2cf418eaeb8a24efb32a
  "\u8282\u70B9\u5750\u6807": {x: -91.000046, y: 1011}
  "\u5B50\u8282\u70B9":
  - {fileID: -1292942016525015846}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u62B1\u6B49\uFF0C\u8BA9\u4F60\u60F3\u8D77\u4E86\u4E0D\u6109\u5FEB\u7684\u8FC7\u53BB\u3002\u6B22\u8FCE\u6765\u5230\u6708\u89C1\u6751\uFF0C\u6211\u53EB\u57C3\u5FB7\u8499\uFF0C\u5982\u4F60\u6240\u89C1\uFF0C\u6211\u662F\u4E00\u540D\u9A91\u58EB\u3002"
--- !u!114 &5057465199843733920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f25680c424fa43b4ab41edc6b8dc3e91
  "\u8282\u70B9\u5750\u6807": {x: -72.562515, y: 794}
  "\u5B50\u8282\u70B9":
  - {fileID: 7365370753255634302}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &5706061713931194760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 39a4554a7760fd244a55c73d6d725101
  "\u8282\u70B9\u5750\u6807": {x: 25.000168, y: 32.856308}
  "\u5B50\u8282\u70B9":
  - {fileID: -1017407810297934250}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 0
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u5C11\u5E74"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8BF7\u2026\u2026\u8BF7\u7B49\u4E00\u7B49\u3002"
--- !u!114 &5866623553554761765
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4c0e30e5791b3474d84af89a0517ead3
  "\u8282\u70B9\u5750\u6807": {x: 2455, y: 805.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: -151604714131417213}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u548C\u6211\u7684\u961F\u4F0D\u5728\u5927\u5C71\u4E2D\u8D70\u6563\uFF0C\u4ED6\u4EEC\u90FD\u5931\u8E2A\u4E86\uFF0C\u53EA\u6709\u6211\u6765\u5230\u8FD9\u4E2A\u6751\u5E84\u3002"
--- !u!114 &5910608102519280781
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": b4a1f5d451ac58048903dd918e5d1f54
  "\u8282\u70B9\u5750\u6807": {x: 2463, y: 1190}
  "\u5B50\u8282\u70B9":
  - {fileID: -4938153764385865893}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u82F1\u52C7\u3001\u6B63\u4E49\u3001\u5FE0\u8BDA\u3001\u5949\u732E\u2026\u2026\u65E0\u8BBA\u8EAB\u5904\u4F55\u5730\uFF0C\u9762\u5BF9\u600E\u6837\u7684\u73AF\u5883\uFF0C\u9A91\u58EB\u90FD\u5E94\u5F53\u505A\u5230\u8FD9\u4E9B\uFF0C\u54EA\u6015\u53EA\u662F\u89C1\u4E60\uFF0C\u4E5F\u5E94\u8BE5\u5C65\u884C\u8D23\u4EFB\u3002"
--- !u!114 &5989715467250499380
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": d70690552ae269e42ae6c28a67795dca
  "\u8282\u70B9\u5750\u6807": {x: 3114, y: 970.4198}
  "\u5B50\u8282\u70B9":
  - {fileID: 2703104476277605637}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &6056332120517694583
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 25a5412316e81d44b875194a1d1c5ebc
  "\u8282\u70B9\u5750\u6807": {x: -134, y: 612}
  "\u5B50\u8282\u70B9":
  - {fileID: 5057465199843733920}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u5C11\u5E74"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F46\u4F60\u662F\u600E\u4E48\u6765\u5230\u8FD9\u91CC\u7684\u5462\uFF1F\u6708\u89C1\u6751\u5F88\u4E45\u6CA1\u6709\u5916\u4EBA\u6765\u8FC7\u4E86\u2026\u2026\u4E3A\u3001\u4E3A\u4E86\u5927\u5BB6\u7684\u5B89\u5168\uFF0C\u6211\u5FC5\u987B\u95EE\u6E05\u695A\u3002"
--- !u!114 &6216363080202810879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 0f294290340885f4f98b0f358f7e5546
  "\u8282\u70B9\u5750\u6807": {x: 785.99994, y: 357.332}
  "\u5B50\u8282\u70B9":
  - {fileID: -3757369247680473989}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8BF4\u8D77\u6765\uFF0C\u4F60\u8EAB\u4E0A\u90A3\u628A\u6B66\u5668\u770B\u4E0A\u53BB\u5F88\u5389\u5BB3\u5462\uFF0C\u4E0D\u6127\u662F\u5192\u9669\u8005\uFF01"
--- !u!114 &7365370753255634302
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u544A\u77E5\u539F\u7531"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 203fc8b3cef9ca040918b468142a45d1
  "\u8282\u70B9\u5750\u6807": {x: -597.00006, y: 918.00006}
  "\u5B50\u8282\u70B9":
  - {fileID: 4907565669281974943}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u5C11\u5E74"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u62A4\u9001\u5546\u961F\uFF0C\u5229\u5965\u62C9\u65AF\u5927\u706B\u2026\u2026\u539F\u6765\u5982\u6B64\uFF0C\u4F60\u4E5F\u662F\u56E0\u4E3A\u90A3\u573A\u5927\u706B\u2026\u2026\u5509\uFF0C\u5F88\u591A\u4EBA\u90FD\u88AB\u5B83\u6539\u53D8\u4E86\u547D\u8FD0\uFF0C\u867D\u7136\u5DF2\u7ECF\u8FC7\u53BB\u4E86\u5F88\u4E45\uFF0C\u4F46\u90A3\u4ECD\u662F\u4E00\u9053\u65E0\u6CD5\u5FD8\u6000\u7684\u4F24\u53E3\u2026\u2026"
--- !u!114 &7438690791645675423
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": a6f1158bdfd6d6f45a12e650261221dd
  "\u8282\u70B9\u5750\u6807": {x: 3035, y: 775.41974}
  "\u5B50\u8282\u70B9":
  - {fileID: 5989715467250499380}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u59D0\u59D0\u66FE\u7ECF\u5938\u5956\u8FC7\u6211\uFF0C\u8BF4\u6211\u505A\u7684\u98DF\u7269\u80FD\u591F\u8BA9\u4EBA\u6062\u590D\u7CBE\u795E\u3002\u4E0D\u77E5\u9053\u5979\u73B0\u5728\u5728\u54EA\u91CC\uFF0C\u662F\u5426\u5E73\u5B89\u2026\u2026"
--- !u!114 &7962311503985772671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c63c0b701f71d04bad40980ae3dd6dd, type: 3}
  m_Name: WeaponUpgradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u6253\u78E8\u6B66\u5668"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 16af4817e1ee7324dadafb572459ba75
  "\u8282\u70B9\u5750\u6807": {x: 609.0358, y: 1208.7886}
  "\u5B50\u8282\u70B9":
  - {fileID: 193073000187554423}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &8428387671464012926
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 8ff707370f7aee74d9247c340bd31252
  "\u8282\u70B9\u5750\u6807": {x: 878.0357, y: 1000.7885}
  "\u5B50\u8282\u70B9":
  - {fileID: 7962311503985772671}
  - {fileID: 4878508347537005924}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
