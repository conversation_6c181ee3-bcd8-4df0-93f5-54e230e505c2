%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-6098544418182922726
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Windmill_Fabric
  m_Shader: {fileID: 4800000, guid: b7839dad95683814aa64166edc107ae2, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _EMISSION
  - _METALLICSPECGLOSSMAP
  - _NORMALMAP
  m_InvalidKeywords:
  - _DETAILSOURCE_SPECULARALPHA
  - _ENABLETRIPLANARPROJECTION_ON
  - _FLIPBOOKBLENDING_OFF
  - _METALLICGLOSSMAP
  - _SOURCE_SPECULARALPHA
  - _TRIPLANARSPACEPROJECTION_OBJECTSPACE
  - _USEAOFROMMAINPROPERTIES_ON
  - _USEBASECOLORALPHA_ON
  - _USEEMISSIONFROMMAINPROPERTIES_ON
  - _WINDQUALITY_NONE
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses:
  - GRABPASS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: e15b7f2f734d893428a7cb3f297f55d3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 2d4dd9568caa10642b743fdb128d0c75, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CutoutMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailSpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ExtraTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: e15b7f2f734d893428a7cb3f297f55d3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: b947da520af5a8440953d2fbea604ea4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowOffset:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TerrainHolesTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TranslucencyMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransparentMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - PixelSnap: 0
    - _AO: 2.4
    - _AlphaClip: 1
    - _AlphaToMask: 1
    - _AoIntensity: 1
    - _BaseLight: 0.35
    - _BillboardKwToggle: 0
    - _BillboardShadowFade: 0.5
    - _Blend: 0
    - _BlendMainNormal: 0
    - _BlendModePreserveSpecular: 1
    - _BlendOp: 0
    - _BlendmodeOverlay: 0
    - _Brightness: 1
    - _BumpScale: 1
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _ColorMode: 0
    - _ContrastDetailMap: 0
    - _ContrastTransparentMap: 0
    - _Cull: 0
    - _Cutoff: 0.932
    - _DetailBrightness: 1
    - _DetailGlossiness: 0.5
    - _DetailNormalMapScale: 1
    - _DetailSaturation: 0
    - _DetailSource: 0
    - _DetailSpecularIntensity: 0.2
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissionEnabled: 0
    - _EmissionIntensity: 1
    - _EnableDetailMask: 0
    - _EnableExternalAlpha: 0
    - _EnableTriplanarProjection: 1
    - _FlipbookBlending: 0
    - _FlipbookMode: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.38
    - _GlossyReflections: 1
    - _HueVariationKwToggle: 0
    - _IntensityCutoutMap: 0.5
    - _IntensityMask: 1
    - _IntensityTransparentMap: 0
    - _InvertABaseColor: 0
    - _InvertCutout: 0
    - _InvertMask: 0
    - _InvertTransparent: 0
    - _LightingEnabled: 1
    - _Metallic: 0
    - _Mode: 1
    - _NormalMapKwToggle: 0
    - _Occlusion: 7.5
    - _OcclusionStrength: 1
    - _Parallax: 0.001
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Saturation: 0
    - _ShadowStrength: 1
    - _Shininess: 0.127
    - _Smoothness: 0.38
    - _SmoothnessTextureChannel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _Source: 0
    - _SourceMask: 0
    - _SpecularHighlights: 1
    - _SpecularIntensity: 0.25
    - _SpreadDetailMap: 0
    - _SpreadTransparentMap: 0
    - _SquashAmount: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _SubsurfaceIndirect: 0.25
    - _SubsurfaceKwToggle: 0
    - _Surface: 0
    - _TranslucencyViewDependency: 0.7
    - _TriplanarFalloff: 20
    - _TriplanarSpaceProjection: 0
    - _TwoSided: 2
    - _UVSec: 0
    - _UseAoFromMainProperties: 1
    - _UseBaseColorAlpha: 1
    - _UseEmissionFromMainProperties: 1
    - _VisualizeMask: 0
    - _WindQuality: 0
    - _ZTest: 2
    - _ZWrite: 1
    - __dirty: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColorAddSubDiff: {r: 0, g: 0, b: 0, a: 0}
    - _CameraFadeParams: {r: 0, g: Infinity, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DetailColor: {r: 1, g: 1, b: 1, a: 0}
    - _DetailSpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _GlobalXYTilingXYZWOffsetXY: {r: 1, g: 1, b: 0, a: 0}
    - _HueVariation: {r: 1, g: 0.5, b: 0, a: 0.1}
    - _HueVariationColor: {r: 1, g: 0.5, b: 0, a: 0.1}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _SubsurfaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _TranslucencyColor: {r: 0.73, g: 0.85, b: 0.41, a: 1}
    - _TreeInstanceColor: {r: 1, g: 1, b: 1, a: 1}
    - _TreeInstanceScale: {r: 1, g: 1, b: 1, a: 1}
    - _TriplanarXYTilingXYZWOffsetXY: {r: 1, g: 1, b: 0, a: 0}
    - _TriplanarXYTilingXYZWOffsetXY1: {r: 1, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
