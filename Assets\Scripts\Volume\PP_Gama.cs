using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class PP_Gama : PP_Base
{
    internal static PP_Gama instance;

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            if (instance != this)
            {
                Destroy(gameObject);
            }
        }
    }

    [ShowInInspector]
    private LiftGammaGain liftGammaGain
    {
        get
        {
            if (volumeProfile == null)
            {
                Debug.LogError("PP_Gama: VolumeProfile 为空！请确保 GameObject 上有 Volume 组件并且配置了 Profile。");
                return null;
            }

            LiftGammaGain lgg;
            if (volumeProfile.TryGet<LiftGammaGain>(out lgg))
            {
                return lgg;
            }
            else
            {
                // 如果当前 profile 中没有 LiftGammaGain，尝试添加一个
                Debug.Log("PP_Gama: 正在添加 LiftGammaGain 组件到 VolumeProfile");
                lgg = volumeProfile.Add<LiftGammaGain>();
                lgg.active = true;

                // 设置默认的 override 状态
                lgg.lift.overrideState = true;
                lgg.gamma.overrideState = true;
                lgg.gain.overrideState = true;

                return lgg;
            }
        }
    }

    [Title("Lift Gamma Gain 控制")]
    [ShowInInspector, LabelText("启用效果")]
    public bool IsActive
    {
        get => liftGammaGain?.active ?? false;
        set
        {
            if (liftGammaGain != null)
                liftGammaGain.active = value;
        }
    }

    [Title("Lift (阴影/暗部)")]
    [ShowInInspector, LabelText("Lift R (红色阴影)"), PropertyRange(0f, 2f)]
    public float LiftR
    {
        get => liftGammaGain?.lift.value.x ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.x = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Lift G (绿色阴影)"), PropertyRange(0f, 2f)]
    public float LiftG
    {
        get => liftGammaGain?.lift.value.y ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.y = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Lift B (蓝色阴影)"), PropertyRange(0f, 2f)]
    public float LiftB
    {
        get => liftGammaGain?.lift.value.z ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.z = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Lift 强度"), PropertyRange(-1f, 1f)]
    public float LiftIntensity
    {
        get => liftGammaGain?.lift.value.w ?? 0f;
        set
        {
            if (liftGammaGain != null)
            {
                var lift = liftGammaGain.lift.value;
                lift.w = value;
                liftGammaGain.lift.value = lift;
                liftGammaGain.lift.overrideState = true;
            }
        }
    }

    [Title("Gamma (中间调)")]
    [ShowInInspector, LabelText("Gamma R (红色中间调)"), PropertyRange(0f, 2f)]
    public float GammaR
    {
        get => liftGammaGain?.gamma.value.x ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.x = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gamma G (绿色中间调)"), PropertyRange(0f, 2f)]
    public float GammaG
    {
        get => liftGammaGain?.gamma.value.y ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.y = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gamma B (蓝色中间调)"), PropertyRange(0f, 2f)]
    public float GammaB
    {
        get => liftGammaGain?.gamma.value.z ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.z = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gamma 强度"), PropertyRange(-1f, 1f)]
    public float GammaIntensity
    {
        get => liftGammaGain?.gamma.value.w ?? 0f;
        set
        {
            if (liftGammaGain != null)
            {
                var gamma = liftGammaGain.gamma.value;
                gamma.w = value;
                liftGammaGain.gamma.value = gamma;
                liftGammaGain.gamma.overrideState = true;
            }
        }
    }

    [Title("Gain (高光/亮部)")]
    [ShowInInspector, LabelText("Gain R (红色高光)"), PropertyRange(0f, 2f)]
    public float GainR
    {
        get => liftGammaGain?.gain.value.x ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.x = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gain G (绿色高光)"), PropertyRange(0f, 2f)]
    public float GainG
    {
        get => liftGammaGain?.gain.value.y ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.y = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gain B (蓝色高光)"), PropertyRange(0f, 2f)]
    public float GainB
    {
        get => liftGammaGain?.gain.value.z ?? 1f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.z = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [ShowInInspector, LabelText("Gain 强度"), PropertyRange(-1f, 1f)]
    public float GainIntensity
    {
        get => liftGammaGain?.gain.value.w ?? 0f;
        set
        {
            if (liftGammaGain != null)
            {
                var gain = liftGammaGain.gain.value;
                gain.w = value;
                liftGammaGain.gain.value = gain;
                liftGammaGain.gain.overrideState = true;
            }
        }
    }

    [Title("便捷控制方法")]
    [Button("重置所有参数")]
    public void ResetAll()
    {
        Debug.Log("PP_Gama: 开始重置所有参数");

        if (liftGammaGain != null)
        {
            liftGammaGain.lift.value = new Vector4(1f, 1f, 1f, 0f);
            liftGammaGain.gamma.value = new Vector4(1f, 1f, 1f, 0f);
            liftGammaGain.gain.value = new Vector4(1f, 1f, 1f, 0f);

            liftGammaGain.lift.overrideState = true;
            liftGammaGain.gamma.overrideState = true;
            liftGammaGain.gain.overrideState = true;
            liftGammaGain.active = true;

            Debug.Log("PP_Gama: 重置完成");
        }
        else
        {
            Debug.LogError("PP_Gama: 无法重置 - LiftGammaGain 组件为空");
        }
    }

    [Button("设置整体 Gamma")]
    public void SetOverallGamma([LabelText("Gamma 值")] float gammaValue = 1.2f)
    {
        Debug.Log($"PP_Gama: 设置整体 Gamma 为 {gammaValue}");

        if (liftGammaGain != null)
        {
            liftGammaGain.gamma.value = new Vector4(gammaValue, gammaValue, gammaValue, 0f);
            liftGammaGain.gamma.overrideState = true;
            liftGammaGain.active = true;

            Debug.Log($"PP_Gama: Gamma 设置完成，当前值: {liftGammaGain.gamma.value}");
        }
        else
        {
            Debug.LogError("PP_Gama: 无法设置 Gamma - LiftGammaGain 组件为空");
        }
    }

    [Button("设置整体 Gain")]
    public void SetOverallGain([LabelText("Gain 值")] float gainValue = 1.1f)
    {
        Debug.Log($"PP_Gama: 设置整体 Gain 为 {gainValue}");

        if (liftGammaGain != null)
        {
            liftGammaGain.gain.value = new Vector4(gainValue, gainValue, gainValue, 0f);
            liftGammaGain.gain.overrideState = true;
            liftGammaGain.active = true;

            Debug.Log($"PP_Gama: Gain 设置完成，当前值: {liftGammaGain.gain.value}");
        }
        else
        {
            Debug.LogError("PP_Gama: 无法设置 Gain - LiftGammaGain 组件为空");
        }
    }

    [Button("设置整体 Lift")]
    public void SetOverallLift([LabelText("Lift 值")] float liftValue = 1.0f)
    {
        Debug.Log($"PP_Gama: 设置整体 Lift 为 {liftValue}");

        if (liftGammaGain != null)
        {
            liftGammaGain.lift.value = new Vector4(liftValue, liftValue, liftValue, 0f);
            liftGammaGain.lift.overrideState = true;
            liftGammaGain.active = true;

            Debug.Log($"PP_Gama: Lift 设置完成，当前值: {liftGammaGain.lift.value}");
        }
        else
        {
            Debug.LogError("PP_Gama: 无法设置 Lift - LiftGammaGain 组件为空");
        }
    }

    [Button("检查组件状态")]
    public void CheckComponentStatus()
    {
        Debug.Log("=== PP_Gama 组件状态检查 ===");

        // 检查 Volume 组件
        var volume = GetComponent<Volume>();
        if (volume == null)
        {
            Debug.LogError("PP_Gama: 未找到 Volume 组件！");
            return;
        }
        Debug.Log($"PP_Gama: Volume 组件存在，isGlobal: {volume.isGlobal}");

        // 检查 VolumeProfile
        if (volumeProfile == null)
        {
            Debug.LogError("PP_Gama: VolumeProfile 为空！");
            return;
        }
        Debug.Log($"PP_Gama: VolumeProfile 存在: {volumeProfile.name}");

        // 检查 LiftGammaGain 组件
        if (liftGammaGain == null)
        {
            Debug.LogError("PP_Gama: LiftGammaGain 组件为空！");
            return;
        }

        Debug.Log($"PP_Gama: LiftGammaGain 组件存在");
        Debug.Log($"  - Active: {liftGammaGain.active}");
        Debug.Log($"  - Lift Override: {liftGammaGain.lift.overrideState}, Value: {liftGammaGain.lift.value}");
        Debug.Log($"  - Gamma Override: {liftGammaGain.gamma.overrideState}, Value: {liftGammaGain.gamma.value}");
        Debug.Log($"  - Gain Override: {liftGammaGain.gain.overrideState}, Value: {liftGammaGain.gain.value}");

        Debug.Log("=== 状态检查完成 ===");
    }

    /// <summary>
    /// 设置完整的 Lift Gamma Gain 参数
    /// </summary>
    /// <param name="lift">Lift 值 (阴影)</param>
    /// <param name="gamma">Gamma 值 (中间调)</param>
    /// <param name="gain">Gain 值 (高光)</param>
    public void SetLiftGammaGain(Vector4 lift, Vector4 gamma, Vector4 gain)
    {
        Debug.Log($"PP_Gama: 设置完整参数 - Lift: {lift}, Gamma: {gamma}, Gain: {gain}");

        if (liftGammaGain != null)
        {
            liftGammaGain.lift.value = lift;
            liftGammaGain.gamma.value = gamma;
            liftGammaGain.gain.value = gain;

            liftGammaGain.lift.overrideState = true;
            liftGammaGain.gamma.overrideState = true;
            liftGammaGain.gain.overrideState = true;
            liftGammaGain.active = true;

            Debug.Log("PP_Gama: 完整参数设置完成");
        }
        else
        {
            Debug.LogError("PP_Gama: 无法设置完整参数 - LiftGammaGain 组件为空");
        }
    }

    /// <summary>
    /// 获取当前的 Lift Gamma Gain 参数
    /// </summary>
    /// <returns>包含 lift, gamma, gain 的元组</returns>
    public (Vector4 lift, Vector4 gamma, Vector4 gain) GetLiftGammaGain()
    {
        if (liftGammaGain != null)
        {
            return (liftGammaGain.lift.value, liftGammaGain.gamma.value, liftGammaGain.gain.value);
        }
        return (Vector4.one, Vector4.one, Vector4.one);
    }
}
