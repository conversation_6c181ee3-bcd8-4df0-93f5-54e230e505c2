%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9142197617299156545
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4f99d721fdfad3c49a83457b9741203d
  "\u8282\u70B9\u5750\u6807": {x: 1386.9999, y: 1652}
  "\u5B50\u8282\u70B9":
  - {fileID: 1710295553121401996}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u62FF\u51FA\u4F60\u7684\u6B66\u5668\u3002"
--- !u!114 &-9005140830838530426
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 64faaee6127d53b4ba83d573bc475cda
  "\u8282\u70B9\u5750\u6807": {x: -852, y: 1914}
  "\u5B50\u8282\u70B9":
  - {fileID: -6837913331767995079}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u6216\u8BB8\u6211\u8BE5\u8C22\u8C22\u4F60\u3002"
--- !u!114 &-8648572738788307310
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u4ECD\u65E7\u575A\u6301"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": ed6105f73e3dd9840a552a3c83cf5944
  "\u8282\u70B9\u5750\u6807": {x: 1379, y: 1450}
  "\u5B50\u8282\u70B9":
  - {fileID: -9142197617299156545}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u597D\u5427\uFF0C\u597D\u5427\u2026\u2026\u6211\u660E\u767D\u4E86\u3002"
--- !u!114 &-7461236475965118587
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u4E0D\u662F\u73A9\u7B11"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": e553e66878a177847a70182388664b69
  "\u8282\u70B9\u5750\u6807": {x: 811, y: 1474}
  "\u5B50\u8282\u70B9":
  - {fileID: 1396056874205097350}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u4E2D\u5E74\u7537\u4EBA"
  "\u5BF9\u8BDD\u5185\u5BB9": "\uFF08\u4E2D\u5E74\u7537\u4EBA\u4E0A\u4E0B\u6253\u91CF\u4E86\u4F60\u597D\u4E00\u4F1A\uFF09"
--- !u!114 &-6842622476764200244
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 5d20b054dd690f04996ad299f42be9ef
  "\u8282\u70B9\u5750\u6807": {x: -8.815094, y: 1156}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E5F\u8BB8\u5230\u4E86\u5229\u5965\u62C9\u65AF\u4F60\u5C31\u4F1A\u53D1\u73B0\uFF0C\u5B66\u8FD9\u4E9B\u4E5F\u662F\u767D\u642D\u2026\u2026\u8C01\u77E5\u9053\u5462\uFF0C\u591A\u6D3B\u4E00\u523B\u7B97\u4E00\u523B\u5427\u3002"
--- !u!114 &-6837913331767995079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 9850c2dfb47a63c41a084755bbce26db
  "\u8282\u70B9\u5750\u6807": {x: -916, y: 2108}
  "\u5B50\u8282\u70B9":
  - {fileID: 9610240707333050}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u641E\u51FA\u8FD9\u4E48\u5927\u52A8\u9759\uFF0C\u8FD9\u4E0B\u8FDE\u6211\u4E5F\u60F3\u518D\u52AA\u529B\u770B\u770B\u4E86\u3002"
--- !u!114 &-6747380624664229087
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 7f620ccea45a9ac40ab9ffd469fecf2c
  "\u8282\u70B9\u5750\u6807": {x: 325.9999, y: 789}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u65E0\u8BBA\u662F\u8D27\u7269\u8FD8\u662F\u6218\u6280\uFF0C\u6211\u90FD\u4F1A\u5C3D\u529B\u51C6\u5907\u7684\u3002"
--- !u!114 &-6746851345293492876
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5F53\u7136"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 87a3140d45fcf1c488a6ccd6dae90790
  "\u8282\u70B9\u5750\u6807": {x: 1347, y: 1044}
  "\u5B50\u8282\u70B9":
  - {fileID: 1875149336073009807}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u662F\u554A\uFF0C\u662F\u554A\uFF0C\u4F60\u5F53\u7136\u8981\u53BB\u2026\u2026"
--- !u!114 &-6744724323705905217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u79BB\u5F00"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 9b202d67b564f764295d26c94ab23463
  "\u8282\u70B9\u5750\u6807": {x: 2664.0796, y: 560}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u518D\u6765\u54E6\u2026\u2026\u5F53\u4F60\u60F3\u4E70\u70B9\u4EC0\u4E48\uFF0C\u6216\u8005\u5B66\u70B9\u4EC0\u4E48\u7684\u65F6\u5019\u3002"
--- !u!114 &-6570891328875025348
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 84eab4b64807f9b4ba6e81fb0bc41ebf
  "\u8282\u70B9\u5750\u6807": {x: -868, y: 711}
  "\u5B50\u8282\u70B9":
  - {fileID: -1136516111183662490}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5F53\u7136\u8FD9\u662F\u6700\u7CDF\u7684\u60C5\u51B5\u3002"
--- !u!114 &-6433306470098347349
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": eb390198d6e45ce40b6a0cd4c3076915
  "\u8282\u70B9\u5750\u6807": {x: 795, y: 676}
  "\u5B50\u8282\u70B9":
  - {fileID: -1829745824996782990}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-4381894988454890172
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f60da47c3ecc6cc40a80bf579538c8c0
  "\u8282\u70B9\u5750\u6807": {x: 1323, y: 81}
  "\u5B50\u8282\u70B9":
  - {fileID: 701968438830077936}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u5206\u652F\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 2
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8BF4\u5230\u8FD9\u91CC\uFF0C\u4F60\u5E94\u8BE5\u77E5\u9053\u4E86\u5427\uFF1F"
--- !u!114 &-3653370223448223687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 5cfbc1b633a646c42a00ca7fc0e8d554
  "\u8282\u70B9\u5750\u6807": {x: 1407.7515, y: 2074}
  "\u5B50\u8282\u70B9":
  - {fileID: 1840513226474932056}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u597D\u4E0D\u5BB9\u6613\u6253\u5F00\u4E86\u8981\u585E\u7684\u901A\u8DEF\uFF0C\u4F60\u603B\u4E0D\u4F1A\u662F\u4E3A\u4E86\u53BB\u9996\u90FD\u9001\u6B7B\u5427\uFF1F"
--- !u!114 &-2899666905633208788
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 5d413ac5070c5ee42a47bcd7da06e6a9
  "\u8282\u70B9\u5750\u6807": {x: 1339, y: 665}
  "\u5B50\u8282\u70B9":
  - {fileID: 7154600185229626487}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E0D\u7528\u8BF4\uFF0C\u4F60\u80AF\u5B9A\u4E5F\u8981\u53BB\u5427\uFF1F"
--- !u!114 &-2617848519877923446
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 5c94fcec8a5f0974d8f1423572e75534
  "\u8282\u70B9\u5750\u6807": {x: -1433.9998, y: 120.00001}
  "\u5B50\u8282\u70B9":
  - {fileID: -1935935377551030789}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 3
      "\u503C": 3
      costItem: 0
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 2
      "\u503C": 9
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u6211\u542C\u5230\u8981\u585E\u91CC\u4F20\u51FA\u53F7\u89D2\u58F0\uFF0C\u90A3\u662F\u53EA\u6709\u901A\u5411\u9996\u90FD\u7684\u5927\u95E8\u88AB\u5F00\u542F\u65F6\u624D\u4F1A\u54CD\u8D77\u7684\u2014\u2014\u662F\u4F60\uFF1F\u4F60\u628A\u90A3\u4E2A\u5927\u5143\u5E05\u6253\u8D25\u4E86\u2026\u2026"
--- !u!114 &-2413971442109816014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e22d3dc528e16da448b89c5efda8eff6, type: 3}
  m_Name: TradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5B66\u4E60\u6218\u6280"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4fb22d690b53cfe4bbe847b23111678b
  "\u8282\u70B9\u5750\u6807": {x: -865, y: 1507}
  "\u5B50\u8282\u70B9":
  - {fileID: 3628504934868959629}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u5546\u4EBASO\u6587\u4EF6": {fileID: 11400000, guid: 9654abf7fbf48844983e736920d5e8e4,
    type: 2}
--- !u!114 &-2213039979320379188
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": e7616b763aabd8442958562c2a0df3e1
  "\u8282\u70B9\u5750\u6807": {x: -407, y: 947.99994}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E0D\u8FC7\u6216\u8BB8\u4F60\u80FD\u505A\u5230\u2026\u2026\u54FC\u54FC\u2026\u2026\u50CF\u8FD9\u6837\u76F8\u4FE1\u70B9\u4EC0\u4E48\u611F\u89C9\u597D\u50CF\u4E5F\u4E0D\u8D56\u3002"
--- !u!114 &-2195330297610875292
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 80cebe4b2d305dc4fa17352dfc7a73a9
  "\u8282\u70B9\u5750\u6807": {x: -414.9999, y: 750}
  "\u5B50\u8282\u70B9":
  - {fileID: -2213039979320379188}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u4E0D\u592A\u76F8\u4FE1\u5728\u5F3A\u5927\u7684\u529B\u91CF\u9762\u524D\uFF0C\u666E\u901A\u4EBA\u7684\u6297\u4E89\u6709\u4EC0\u4E48\u4F5C\u7528\uFF0C\u5C31\u50CF\u8682\u8681\u4E0D\u53EF\u80FD\u586B\u5E73\u5927\u6D77\u3002"
--- !u!114 &-1985758329749587564
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 08c1387a743b89a46a1c541fe659dd22
  "\u8282\u70B9\u5750\u6807": {x: -860, y: 1311}
  "\u5B50\u8282\u70B9":
  - {fileID: -2413971442109816014}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-1935935377551030789
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 9
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 60ecc86f4c2a54944ab534a41ee749ea
  "\u8282\u70B9\u5750\u6807": {x: -1442.2471, y: 310.2629}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5B9E\u5728\u4EE4\u4EBA\u5403\u60CA\uFF01\u65E0\u6CD5\u7A81\u7834\u7684\u5821\u5792\uFF0C\u6218\u65E0\u4E0D\u80DC\u7684\u5143\u5E05\uFF0C\u5C31\u8FD9\u6837\u2026\u2026"
--- !u!114 &-1905782159181927876
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": de03b222d45f6bb4bbe05dcd4d680f57
  "\u8282\u70B9\u5750\u6807": {x: 1909.9999, y: 290}
  "\u5B50\u8282\u70B9":
  - {fileID: -1382602131476064387}
  - {fileID: 9110369953215750187}
  - {fileID: -6744724323705905217}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-1897955008141613534
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 34d314581810b6c488f2eafee601de79
  "\u8282\u70B9\u5750\u6807": {x: 1288, y: 487}
  "\u5B50\u8282\u70B9":
  - {fileID: -2899666905633208788}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u653E\u7740\u5B89\u5168\u7684\u5730\u65B9\u4E0D\u5F85\uFF0C\u975E\u8981\u5230\u9996\u90FD\u51D1\u70ED\u95F9\u2026\u2026\u8FD9\u91CC\u7684\u4E00\u6F6D\u6B7B\u6C34\uFF0C\u90FD\u88AB\u4F60\u6405\u6D3B\u4E86\u2026\u2026"
--- !u!114 &-1829745824996782990
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u662F\u6211"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": a4b798f5a269a654baf86d05b8e815fa
  "\u8282\u70B9\u5750\u6807": {x: 803, y: 866}
  "\u5B50\u8282\u70B9":
  - {fileID: 7721800289651912932}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u4E2D\u5E74\u7537\u4EBA"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u2026\u2026\u2026\u2026"
--- !u!114 &-1787862654222680012
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 243ba997fda9a7a4d93837c7613e11a8
  "\u8282\u70B9\u5750\u6807": {x: -830, y: 1110}
  "\u5B50\u8282\u70B9":
  - {fileID: -1985758329749587564}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6216\u8BB8\u80FD\u5E2E\u4F60\u907F\u514D\u6700\u7CDF\u7CD5\u7684\u60C5\u51B5\u3002"
--- !u!114 &-1382602131476064387
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e22d3dc528e16da448b89c5efda8eff6, type: 3}
  m_Name: TradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u8D2D\u4E70"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 7e224e559ac2fe54990a15b32845baa4
  "\u8282\u70B9\u5750\u6807": {x: 1843.7563, y: 551.9409}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u5546\u4EBASO\u6587\u4EF6": {fileID: 11400000, guid: a5008d774e1d5984cbdd2a50c141efc2,
    type: 2}
--- !u!114 &-1136516111183662490
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 2fadb7d80fdf97848bdc57290ebbec60
  "\u8282\u70B9\u5750\u6807": {x: -873, y: 902}
  "\u5B50\u8282\u70B9":
  - {fileID: -1787862654222680012}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u6211\u77E5\u9053\u51E0\u62DB\u5F3A\u529B\u7684\u6218\u6280\uFF0C\u5982\u679C\u4F60\u6709\u610F\u613F\u7684\u8BDD\uFF0C\u8981\u4E0D\u8981\u5B66\u5B66\u770B\uFF1F"
--- !u!114 &-911101360043050811
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 353d79020f0dad34786f460d7d64128c
  "\u8282\u70B9\u5750\u6807": {x: 1457.9999, y: 2856.829}
  "\u5B50\u8282\u70B9":
  - {fileID: -467834535984337088}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u54FC\u2026\u2026\u54FC\u54FC\u2026\u2026\u6211\u770B\u6211\u4E5F\u662F\u75AF\u4E86\u2026\u2026"
--- !u!114 &-699315750757490232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f192b5586dfc5814ba3870bb93949d48
  "\u8282\u70B9\u5750\u6807": {x: -265, y: 136}
  "\u5B50\u8282\u70B9":
  - {fileID: 654078147036237100}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 10
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5148\u968F\u4FBF\u770B\u770B\uFF0C\u90FD\u662F\u4E9B\u4E34\u65F6\u7684\u4E1C\u897F\u2026\u2026\u4E0B\u6B21\u6211\u4F1A\u641E\u4E9B\u66F4\u597D\u7684\u3002"
--- !u!114 &-612149587994798053
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u79BB\u5F00"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": e63f9233ed8ac514084799b40833d1e8
  "\u8282\u70B9\u5750\u6807": {x: 422.99985, y: 596}
  "\u5B50\u8282\u70B9":
  - {fileID: -6747380624664229087}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6709\u7A7A\u5E38\u6765\u3002"
--- !u!114 &-492615264131192577
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 178e70b036ddce94dbef6628c0856052
  "\u8282\u70B9\u5750\u6807": {x: 803, y: 479}
  "\u5B50\u8282\u70B9":
  - {fileID: -6433306470098347349}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5230\u5E95\u662F\u8C01\u505A\u7684\u2026\u2026\u4E5F\u592A\u731B\u4E86\u2026\u2026"
--- !u!114 &-467834535984337088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u4F63\u5175\u5206\u652F\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 3
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": aea06758aa179c04f94301ae33cb3537
  "\u8282\u70B9\u5750\u6807": {x: 1415, y: 3035}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6BD4\u8D77\u6559\u4F60\u8FD9\u4E9B\uFF0C\u6211\u5E94\u8BE5\u5C3D\u5FEB\u7ED9\u4F60\u6253\u4E00\u53E3\u68FA\u6750\u624D\u5BF9\u2026\u2026"
--- !u!114 &-91839066128739821
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": b9f36ed700af51f4fbca1fb91ab43b4e
  "\u8282\u70B9\u5750\u6807": {x: -895, y: 517}
  "\u5B50\u8282\u70B9":
  - {fileID: -6570891328875025348}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u597D\u5728\u90A3\u91CC\u6709\u9664\u4E86\u738B\u56FD\u6559\u4F1A\u603B\u90E8\u4E4B\u5916\u6700\u5927\u7684\u9ED1\u591C\u6559\u5802\uFF0C\u4E07\u4E00\uFF0C\u6211\u662F\u8BF4\u4E07\u4E00\uFF0C\u5FEB\u6B7B\u7684\u65F6\u5019\u53EA\u8981\u8BF4\u81EA\u5DF1\u662F\u5973\u795E\u7684\u4FE1\u5F92\uFF0C\u6559\u4F1A\u90A3\u5E2E\u4EBA\u603B\u4E0D\u4F1A\u89C1\u6B7B\u4E0D\u6551\u2026\u2026"
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 204e534958e91824783fa0a7dc10275e, type: 3}
  m_Name: "\u4F63\u5175\u6708\u89C1\u6751\u5BF9\u8BDD4"
  m_EditorClassIdentifier: 
  treeGuid: 0a76cf86abee91948a4331146333a622
  "\u6839\u8282\u70B9": {fileID: 496975805498021638}
  "\u76EE\u524D\u8282\u70B9": {fileID: 0}
  "\u6811\u72B6\u6001": 1
  "\u8282\u70B9\u5217\u8868":
  - {fileID: 496975805498021638}
  - {fileID: -2617848519877923446}
  - {fileID: -1935935377551030789}
  - {fileID: 7031049857175428995}
  - {fileID: 7318659523852514588}
  - {fileID: -91839066128739821}
  - {fileID: -6570891328875025348}
  - {fileID: -1136516111183662490}
  - {fileID: -1787862654222680012}
  - {fileID: -1985758329749587564}
  - {fileID: -2413971442109816014}
  - {fileID: 3628504934868959629}
  - {fileID: -9005140830838530426}
  - {fileID: -6837913331767995079}
  - {fileID: 9610240707333050}
  - {fileID: 132203830543518227}
  - {fileID: -612149587994798053}
  - {fileID: -699315750757490232}
  - {fileID: 654078147036237100}
  - {fileID: -2195330297610875292}
  - {fileID: -2213039979320379188}
  - {fileID: -6747380624664229087}
  - {fileID: 1868473989317406927}
  - {fileID: 8972328648980011316}
  - {fileID: -492615264131192577}
  - {fileID: -6433306470098347349}
  - {fileID: -1829745824996782990}
  - {fileID: 7721800289651912932}
  - {fileID: 3019340988731106199}
  - {fileID: -7461236475965118587}
  - {fileID: 1396056874205097350}
  - {fileID: 8407973904758636207}
  - {fileID: 1874966179957640508}
  - {fileID: 8367998445958699244}
  - {fileID: 3128298059941871318}
  - {fileID: -4381894988454890172}
  - {fileID: 701968438830077936}
  - {fileID: -1897955008141613534}
  - {fileID: -2899666905633208788}
  - {fileID: -6746851345293492876}
  - {fileID: 7154600185229626487}
  - {fileID: 1875149336073009807}
  - {fileID: -8648572738788307310}
  - {fileID: -9142197617299156545}
  - {fileID: 1710295553121401996}
  - {fileID: -3653370223448223687}
  - {fileID: 1930146292920755078}
  - {fileID: -911101360043050811}
  - {fileID: -467834535984337088}
  - {fileID: 7889179500461975268}
  - {fileID: -1905782159181927876}
  - {fileID: -6744724323705905217}
  - {fileID: -1382602131476064387}
  - {fileID: 1023367671978168647}
  - {fileID: 1840513226474932056}
  - {fileID: 4330251732757139615}
  - {fileID: 9110369953215750187}
  - {fileID: 5124523536833196666}
  - {fileID: -6842622476764200244}
--- !u!114 &9610240707333050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 10
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": ac98ead390b399542b0395dbcfe249b9
  "\u8282\u70B9\u5750\u6807": {x: -889, y: 2297}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u4F1A\u5C3D\u529B\u5F04\u70B9\u597D\u8D27\u56DE\u6765\u7684\uFF0C\u8BB0\u5F97\u6709\u7A7A\u5E38\u6765\u3002"
--- !u!114 &132203830543518227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e22d3dc528e16da448b89c5efda8eff6, type: 3}
  m_Name: TradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u8D2D\u4E70"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 2275dc8c7fae2ff489009e5abe8fccae
  "\u8282\u70B9\u5750\u6807": {x: -345, y: 562}
  "\u5B50\u8282\u70B9":
  - {fileID: -2195330297610875292}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u5546\u4EBASO\u6587\u4EF6": {fileID: 11400000, guid: a5008d774e1d5984cbdd2a50c141efc2,
    type: 2}
--- !u!114 &496975805498021638
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f716134a78f37df418d8eb84a3f5229d, type: 3}
  m_Name: ConditionCheckSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": b626e293cb1f8054dab3289c4c2bd462
  "\u8282\u70B9\u5750\u6807": {x: 518.3502, y: -173}
  "\u5B50\u8282\u70B9":
  - {fileID: -2617848519877923446}
  - {fileID: 7031049857175428995}
  - {fileID: -699315750757490232}
  - {fileID: 1868473989317406927}
  - {fileID: -4381894988454890172}
  - {fileID: 7889179500461975268}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &654078147036237100
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 241e4d60079228e4885c96a5394bf772
  "\u8282\u70B9\u5750\u6807": {x: -303, y: 333}
  "\u5B50\u8282\u70B9":
  - {fileID: 132203830543518227}
  - {fileID: 5124523536833196666}
  - {fileID: -612149587994798053}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &701968438830077936
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": fe7aa48f779ce2947b8c69f3ba77fe03
  "\u8282\u70B9\u5750\u6807": {x: 1331, y: 290}
  "\u5B50\u8282\u70B9":
  - {fileID: -1897955008141613534}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u57C3\u5FB7\u8499\u554A\uFF0C\u90A3\u5C0F\u5B50\u597D\u50CF\u4E5F\u6253\u7B97\u79BB\u5F00\uFF0C\u5C31\u5728\u4F60\u6253\u5F00\u8981\u585E\u5927\u95E8\u4E4B\u540E\u2026\u2026"
--- !u!114 &1023367671978168647
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 139fad2265d39fc4b94886f567e04c9a
  "\u8282\u70B9\u5750\u6807": {x: 1488.0001, y: 2459}
  "\u5B50\u8282\u70B9":
  - {fileID: 1930146292920755078}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &1396056874205097350
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": afbd3d3259222f847ab7c384ccb9ee0c
  "\u8282\u70B9\u5750\u6807": {x: 803, y: 1674}
  "\u5B50\u8282\u70B9":
  - {fileID: 4330251732757139615}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u4E2D\u5E74\u7537\u4EBA"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5514\u2026\u2026\u6211\u597D\u50CF\u6709\u70B9\u5370\u8C61\u4E86\uFF0C\u5C31\u662F\u4F60\u554A\u2026\u2026"
--- !u!114 &1710295553121401996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u4EC0\u4E48\u610F\u601D\uFF1F"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f3eac80159274154abab0378426b503c
  "\u8282\u70B9\u5750\u6807": {x: 1387, y: 1876.0001}
  "\u5B50\u8282\u70B9":
  - {fileID: -3653370223448223687}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u77E5\u9053\u51E0\u62DB\u5F3A\u529B\u7684\u6218\u6280\uFF0C\u6211\u60F3\uFF0C\u4E5F\u8BB8\uFF0C\u4F60\u5230\u4E86\u5229\u5965\u62C9\u65AF\u80FD\u7528\u5F97\u4E0A\u2026\u2026"
--- !u!114 &1840513226474932056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 8bedeb341a4c54a4a9d87cecf55bd194
  "\u8282\u70B9\u5750\u6807": {x: 1465, y: 2270}
  "\u5B50\u8282\u70B9":
  - {fileID: 1023367671978168647}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u4F60\u8BF4\u5462\uFF1F"
--- !u!114 &1868473989317406927
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 0c4bb3fe3b3c3584abaaa52056a66268
  "\u8282\u70B9\u5750\u6807": {x: 814, y: 73}
  "\u5B50\u8282\u70B9":
  - {fileID: 8972328648980011316}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u5206\u652F\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 0
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u4E2D\u5E74\u7537\u4EBA"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5662\u2026\u2026\u4F60\u542C\u5230\u4E86\u5417\uFF1F"
--- !u!114 &1874966179957640508
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 494f2b334e8873c4699d13823c5f4d2b
  "\u8282\u70B9\u5750\u6807": {x: 820.99994, y: 2262}
  "\u5B50\u8282\u70B9":
  - {fileID: 8367998445958699244}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8001\u5B9E\u8BF4\uFF0C\u8FD9\u5F88\u6B63\u5E38\u2026\u2026\u8FD9\u624D\u662F\u6B63\u5E38\u7684\u3002"
--- !u!114 &1875149336073009807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 899c8c458520f084594aa3f244ff6dd1
  "\u8282\u70B9\u5750\u6807": {x: 1339, y: 1241}
  "\u5B50\u8282\u70B9":
  - {fileID: -8648572738788307310}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u81EA\u4ECE\u8FD9\u4E2A\u56FD\u5BB6\u53D8\u6210\u73B0\u5728\u8FD9\u6837\u4E4B\u540E\uFF0C\u8FDB\u53BB\u5229\u5965\u62C9\u65AF\u7684\u8FD8\u4E00\u4E2A\u90FD\u6CA1\u51FA\u6765\u2026\u2026\u81F3\u5C11\u6211\u6CA1\u542C\u8BF4\u8FC7\u3002\u4F60\u5C31\u6CA1\u60F3\u8FC7\uFF0C\u5C31\u7B97\u53BB\u4E86\u4E5F\u53EA\u662F\u8BA9\u5229\u5965\u62C9\u65AF\u518D\u591A\u4E00\u5177\u5C38\u4F53\uFF1F"
--- !u!114 &1930146292920755078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e22d3dc528e16da448b89c5efda8eff6, type: 3}
  m_Name: TradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5B66\u4E60\u6218\u6280"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 0639bc646ee86514bb1f1994a832343d
  "\u8282\u70B9\u5750\u6807": {x: 1473, y: 2659}
  "\u5B50\u8282\u70B9":
  - {fileID: -911101360043050811}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u5546\u4EBASO\u6587\u4EF6": {fileID: 11400000, guid: 9654abf7fbf48844983e736920d5e8e4,
    type: 2}
--- !u!114 &3019340988731106199
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f34bfbd85f7cf2446913b7c6319412bc
  "\u8282\u70B9\u5750\u6807": {x: 803, y: 1272}
  "\u5B50\u8282\u70B9":
  - {fileID: -7461236475965118587}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &3128298059941871318
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u4F63\u5175\u5206\u652F\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 3d62c2d2ac4749e41b7b158c826b748a
  "\u8282\u70B9\u5750\u6807": {x: 834.9546, y: 2651}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u770B\u6765\u4E8B\u65E0\u7EDD\u5BF9\uFF0C\u4EC0\u4E48\u602A\u4E8B\u90FD\u6709\u53EF\u80FD\u53D1\u751F\u2026\u2026"
--- !u!114 &3628504934868959629
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 9
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 9a0dccb0749a2d246b16a3d0b2adba38
  "\u8282\u70B9\u5750\u6807": {x: -865, y: 1706}
  "\u5B50\u8282\u70B9":
  - {fileID: -9005140830838530426}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u539F\u4EE5\u4E3A\u8FD9\u4E9B\u518D\u4E5F\u7528\u4E0D\u4E0A\u4E86\u2026\u2026\u8FD8\u597D\u2026\u2026"
--- !u!114 &4330251732757139615
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": ebc2549247a08be4689dd46e43881290
  "\u8282\u70B9\u5750\u6807": {x: 773.00006, y: 1868.0002}
  "\u5B50\u8282\u70B9":
  - {fileID: 8407973904758636207}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u53EB\u8D1D\u514B\uFF0C\u662F\u4E2A\u6D41\u6D6A\u5546\u4EBA\uFF0C\u8FB9\u65C5\u884C\u8FB9\u5356\u70B9\u6742\u8D27\uFF0C\u73B0\u5728\u6682\u65F6\u5B9A\u5C45\u5728\u8FD9\u3002"
--- !u!114 &5124523536833196666
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e22d3dc528e16da448b89c5efda8eff6, type: 3}
  m_Name: TradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5B66\u4E60\u6218\u6280"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 5793f4d2f02d3594983515e78948ddeb
  "\u8282\u70B9\u5750\u6807": {x: 29.000055, y: 588}
  "\u5B50\u8282\u70B9":
  - {fileID: -6842622476764200244}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u5546\u4EBASO\u6587\u4EF6": {fileID: 11400000, guid: 9654abf7fbf48844983e736920d5e8e4,
    type: 2}
--- !u!114 &7031049857175428995
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 1d71ed78f6653e74f988a80ec00cf731
  "\u8282\u70B9\u5750\u6807": {x: -903, y: 125}
  "\u5B50\u8282\u70B9":
  - {fileID: 7318659523852514588}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 9
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8BE5\u4E0D\u8BE5\u8BF4\uFF0C\u6211\u5F00\u59CB\u4F69\u670D\u4F60\u4E86\uFF0C\u6211\u6CA1\u60F3\u5230\u4F60\u771F\u7684\u80FD\u2026\u2026\u603B\u4E4B\uFF0C\u606D\u559C\u554A\uFF0C\u79BB\u5229\u5965\u62C9\u65AF\u53C8\u8FD1\u4E86\u4E00\u6B65\u3002"
--- !u!114 &7154600185229626487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 73c51d676238a8e44a50cb057ff1dda8
  "\u8282\u70B9\u5750\u6807": {x: 1339, y: 854}
  "\u5B50\u8282\u70B9":
  - {fileID: -6746851345293492876}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &7318659523852514588
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": fae3126c26975644cb3e6deb831dd8b7
  "\u8282\u70B9\u5750\u6807": {x: -895, y: 321}
  "\u5B50\u8282\u70B9":
  - {fileID: -91839066128739821}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E0D\u8FC7\u8FD9\u8FD8\u53EA\u662F\u5F00\u59CB\uFF0C\u81EA\u4ECE\u8FD9\u4E2A\u56FD\u5BB6\u53D8\u6210\u73B0\u5728\u8FD9\u6837\u4E4B\u540E\uFF0C\u8FDB\u53BB\u5229\u5965\u62C9\u65AF\u7684\u4E00\u4E2A\u90FD\u6CA1\u51FA\u6765\u2026\u2026\u6CA1\u4EBA\u77E5\u9053\u90A3\u91CC\u662F\u4EC0\u4E48\u60C5\u51B5\uFF0C\u6211\u60F3\u53EA\u4F1A\u6BD4\u8981\u585E\u66F4\u590D\u6742\u3002"
--- !u!114 &7721800289651912932
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4de2555ee1671ce4fb339c62966c7a69
  "\u8282\u70B9\u5750\u6807": {x: 803, y: 1063}
  "\u5B50\u8282\u70B9":
  - {fileID: 3019340988731106199}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u4E2D\u5E74\u7537\u4EBA"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u4F46\u613F\u8FD9\u4E0D\u662F\u4E00\u4E2A\u73A9\u7B11\u3002"
--- !u!114 &7889179500461975268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": c0f21eda99ce4d44ca105916679d1965
  "\u8282\u70B9\u5750\u6807": {x: 1782, y: 81}
  "\u5B50\u8282\u70B9":
  - {fileID: -1905782159181927876}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u4F63\u5175\u5206\u652F\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 3
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6258\u4F60\u7684\u798F\u2026\u2026\u641E\u51FA\u8FD9\u4E48\u5927\u52A8\u9759\uFF0C\u8FD9\u4E0B\u8FDE\u6211\u90FD\u597D\u50CF\u6709\u70B9\u5E72\u52B2\u4E86\u3002"
--- !u!114 &8367998445958699244
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4b460da5c9e037b459beb1a1f08aafeb
  "\u8282\u70B9\u5750\u6807": {x: 826.9546, y: 2450.9998}
  "\u5B50\u8282\u70B9":
  - {fileID: 3128298059941871318}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F46\u6CA1\u60F3\u5230\u4F60\u5C45\u7136\u6CA1\u4E8B\uFF0C\u6D3B\u8E66\u4E71\u8DF3\u5230\u73B0\u5728\uFF0C\u8FD8\u771F\u7684\u6253\u8D25\u4E86\u770B\u5B88\u8981\u585E\u7684\u5143\u5E05\uFF0C\u6253\u5F00\u4E86\u8981\u585E\u7684\u901A\u8DEF\u2026\u2026\u771F\u4EE4\u6211\u610F\u5916\u2026\u2026"
--- !u!114 &8407973904758636207
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 8f39facfe9fd8064c9fe5690edfecf93
  "\u8282\u70B9\u5750\u6807": {x: 758.00006, y: 2066.2432}
  "\u5B50\u8282\u70B9":
  - {fileID: 1874966179957640508}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u8D1D\u514B"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E4B\u524D\u7684\u786E\u542C\u57C3\u5FB7\u8499\u63D0\u8D77\u8FC7\uFF0C\u6709\u4E2A\u5916\u4E61\u4EBA\u6218\u80DC\u4E86\u68EE\u6797\u91CC\u4E00\u68F5\u5F02\u53D8\u7684\u53E4\u8001\u5DE8\u6728\uFF0C\u8FDB\u5165\u4E86\u5FB7\u58A8\u8981\u585E\uFF0C\u4E4B\u540E\u5C31\u6CA1\u97F3\u8BAF\u4E86\u2026\u2026"
--- !u!114 &8972328648980011316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 1cc7b124a0b167041a979ba20e80e18e
  "\u8282\u70B9\u5750\u6807": {x: 757, y: 282}
  "\u5B50\u8282\u70B9":
  - {fileID: -492615264131192577}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: ecc942e56db06b042ab21115fa3a292d, type: 3}
  "\u540D\u5B57": "\u4E2D\u5E74\u7537\u4EBA"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4ECE\u90A3\u4E2A\u8981\u585E\u91CC\u4F20\u51FA\u4E86\u53F7\u89D2\u58F0\uFF0C\u90A3\u662F\u53EA\u6709\u901A\u5411\u9996\u90FD\u7684\u5927\u95E8\u88AB\u5F00\u542F\u65F6\u624D\u4F1A\u54CD\u8D77\u7684\u2026\u2026"
--- !u!114 &9110369953215750187
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e22d3dc528e16da448b89c5efda8eff6, type: 3}
  m_Name: TradeSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5B66\u4E60\u6218\u6280"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 926025580e904514798cdadff2e1bafc
  "\u8282\u70B9\u5750\u6807": {x: 2260.7893, y: 560}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u5546\u4EBASO\u6587\u4EF6": {fileID: 11400000, guid: 9654abf7fbf48844983e736920d5e8e4,
    type: 2}
