%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &29652271490317317
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7485323952173006150}
  m_Layer: 0
  m_Name: "NPC\u4E8B\u4EF61"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7485323952173006150
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 29652271490317317}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2315414970412035}
  - {fileID: 9024539305133663868}
  - {fileID: 9181005638349307249}
  m_Father: {fileID: 0}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1803090154605637931
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5811127109016841047}
  - component: {fileID: 1315852912175529610}
  - component: {fileID: 294972352113208780}
  - component: {fileID: 2218215359691179168}
  - component: {fileID: 5658615151655444206}
  - component: {fileID: 813959906269168924}
  - component: {fileID: 4262616461507561886}
  m_Layer: 0
  m_Name: "\u8DEF\u7EBF\u5224\u5B9A"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5811127109016841047
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 34.5825, y: -31.9399, z: 52.1542}
  m_LocalScale: {x: 68.73754, y: 13.7868, z: 42.18598}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2315414970412035}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1315852912175529610
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &294972352113208780
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2218215359691179168
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5658615151655444206
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  m_impactDropDown: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  selectedOperator: 3
  comparisonValue: 0
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7870161204070906083}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 6609024174806125279}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 2352686094213083550}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 8512535587177189634}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 1803090154605637931}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &813959906269168924
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 4262616461507561886}
--- !u!114 &4262616461507561886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1803090154605637931}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 5658615151655444206}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1 &2352686094213083550
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6959542012847016755}
  - component: {fileID: 8128609188036349947}
  - component: {fileID: 624158407750764598}
  - component: {fileID: 6175452740949167382}
  - component: {fileID: 4949665270879224206}
  - component: {fileID: 3084613371870036918}
  - component: {fileID: 736504123650683425}
  m_Layer: 0
  m_Name: A
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &6959542012847016755
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 34.5825, y: -31.9399, z: 56.2443}
  m_LocalScale: {x: 60, y: 13.7868, z: 14.6516}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2315414970412035}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8128609188036349947
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &624158407750764598
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &6175452740949167382
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 736504123650683425}
--- !u!65 &4949665270879224206
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &3084613371870036918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a62d8f097316182408722a5d81b59f5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  m_impactDropDown: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  value: 2
--- !u!114 &736504123650683425
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352686094213083550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3084613371870036918}
        m_TargetAssemblyTypeName: VariableDictionarySetter, Assembly-CSharp
        m_MethodName: SetVariable
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1 &2560841305701378108
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9024539305133663868}
  m_Layer: 0
  m_Name: "\u843D\u96BE\u6751\u6C11\u7684\u4FE1\u4EF6"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9024539305133663868
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2560841305701378108}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 70.67, y: -13.144, z: 0.25}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3927847338411077117}
  m_Father: {fileID: 7485323952173006150}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3558960121283931048
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2315414970412035}
  m_Layer: 0
  m_Name: "\u6697\u591C\u65A5\u5019\u8DEF\u7EBF\u5224\u5B9A"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2315414970412035
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3558960121283931048}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1003, y: 116.2, z: 2590.4336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6959542012847016755}
  - {fileID: 3302158562232237590}
  - {fileID: 5017959148786909823}
  - {fileID: 674155029041219781}
  - {fileID: 5811127109016841047}
  m_Father: {fileID: 7485323952173006150}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6211446238654361065
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9181005638349307249}
  m_Layer: 0
  m_Name: "\u8FDB\u5165\u8981\u585E\u5E7F\u573A\u53D8\u91CF\u5224\u5B9A"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9181005638349307249
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6211446238654361065}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -819.39276, y: 63.01042, z: 2446.8208}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8652975084923150392}
  m_Father: {fileID: 7485323952173006150}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6609024174806125279
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 674155029041219781}
  - component: {fileID: 3538648656922146298}
  - component: {fileID: 2731573308786738859}
  - component: {fileID: 4839425064660911310}
  - component: {fileID: 6830174330209233777}
  - component: {fileID: 8383727354790983594}
  - component: {fileID: 6656497759060511904}
  m_Layer: 0
  m_Name: "\u8DEF\u7EBF\u5224\u5B9AB"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &674155029041219781
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 34.5825, y: -31.9399, z: 48.4801}
  m_LocalScale: {x: 60, y: 13.7868, z: 30.180067}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2315414970412035}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3538648656922146298
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2731573308786738859
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4839425064660911310
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &6830174330209233777
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BA"
  m_impactDropDown: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BA"
  selectedOperator: 2
  comparisonValue: 0
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8512535587177189634}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 2352686094213083550}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 6609024174806125279}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &8383727354790983594
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 6656497759060511904}
--- !u!114 &6656497759060511904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609024174806125279}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6830174330209233777}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1 &7475436209697173019
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8652975084923150392}
  - component: {fileID: 2027314626721772800}
  - component: {fileID: 4152952392049868958}
  - component: {fileID: 1022161464741141215}
  - component: {fileID: 7743219527393175010}
  - component: {fileID: 8208587692144399363}
  - component: {fileID: 1825526289650874560}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8652975084923150392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.7433, y: 0.58, z: 13.8298}
  m_LocalScale: {x: 34.107216, y: 14.099868, z: 38.76988}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9181005638349307249}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2027314626721772800
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4152952392049868958
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1022161464741141215
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7743219527393175010
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 8208587692144399363}
--- !u!114 &8208587692144399363
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1825526289650874560}
        m_TargetAssemblyTypeName: VariableDictionarySetter, Assembly-CSharp
        m_MethodName: SetVariable
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!114 &1825526289650874560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475436209697173019}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a62d8f097316182408722a5d81b59f5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u8FDB\u5165\u8981\u585E\u5927\u5E7F\u573A"
  m_impactDropDown: "\u8FDB\u5165\u8981\u585E\u5927\u5E7F\u573A"
  value: 1
--- !u!1 &7870161204070906083
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5017959148786909823}
  - component: {fileID: 6783800598224268916}
  - component: {fileID: 7762920424063227788}
  - component: {fileID: 1009271119251777478}
  - component: {fileID: 7020126125560988050}
  - component: {fileID: 3458031667906175093}
  - component: {fileID: 4905708352061478483}
  m_Layer: 0
  m_Name: "\u8DEF\u7EBF\u5224\u5B9AA"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5017959148786909823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 34.5825, y: -31.9399, z: 48.4801}
  m_LocalScale: {x: 60, y: 13.7868, z: 30.180067}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2315414970412035}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6783800598224268916
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7762920424063227788
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1009271119251777478
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7020126125560988050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BA"
  m_impactDropDown: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BA"
  selectedOperator: 3
  comparisonValue: 0
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8512535587177189634}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 2352686094213083550}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 7870161204070906083}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &3458031667906175093
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 4905708352061478483}
--- !u!114 &4905708352061478483
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7870161204070906083}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7020126125560988050}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1 &8400119041305053925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3927847338411077117}
  - component: {fileID: 1455982299473755374}
  - component: {fileID: 5575805335138396797}
  - component: {fileID: 2351606531387345304}
  - component: {fileID: 1592652343227828578}
  - component: {fileID: 320359413466440751}
  - component: {fileID: 3973751840689477473}
  - component: {fileID: *******************}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3927847338411077117
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -808.7811, y: -2.862999, z: 2384.34}
  m_LocalScale: {x: 3.9194825, y: 1, z: 3.3298}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3924085847414417456}
  m_Father: {fileID: 9024539305133663868}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1455982299473755374
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5575805335138396797
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2351606531387345304
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &1592652343227828578
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 1
  useCustomButtonName: 1
  customButtonName: "\u67E5\u770B"
  interaction_Base:
  - {fileID: 320359413466440751}
  - {fileID: *******************}
--- !u!114 &320359413466440751
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3973751840689477473}
        m_TargetAssemblyTypeName: Event_messageWindow, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!114 &3973751840689477473
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d508e9b12ac49141b834351e692902d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  title: "\u843D\u96BE\u6751\u6C11\u7684\u4FE1\u4EF6"
  description: "\u7528\u6728\u70AD\u4E66\u5199\u5728\u7834\u5E03\u4E0A\u7684\u4FE1\u4EF6\uFF0C\u4E00\u4F4D\u843D\u96BE\u6751\u6C11\u7559\u7ED9\u59BB\u5B50\u7684\u7EDD\u7B14\uFF0C\u5B57\u8FF9\u98A4\u6296\u3002\n\n\u7ED9\u6211\u4EB2\u7231\u7684\u59BB\u5B50\u8FC8\u5A05\uFF1A\n\u5F53\u4F60\u770B\u5230\u8FD9\u5C01\u4FE1\u65F6\uFF0C\u6211\u6216\u8BB8\u5DF2\u7ECF\u79BB\u5F00\u4E86\u8FD9\u4E2A\u4E16\u754C\u3002\n\u90A3\u4E00\u5929\u6211\u4EEC\u5728\u5DE8\u5927\u7684\u6050\u60E7\u4E2D\u79BB\u5F00\u79CB\u8C37\u9547\uFF0C\u7136\u800C\uFF0C\u90A3\u573A\u5927\u96FE\u8FD8\u662F\u8BA9\u6211\u5931\u53BB\u4E86\u4F60\u3002\u6211\u65E0\u6BD4\u60B2\u75DB\uFF0C\u53EF\u547D\u8FD0\u4F3C\u4E4E\u4E0D\u80AF\u653E\u8FC7\u6211\uFF0C\u5F53\u6211\u7EC8\u4E8E\u9003\u5230\u8FD9\u5EA7\u8981\u585E\u7948\u6C42\u5E87\u62A4\u65F6\uFF0C\u5374\u88AB\u5F53\u4F5C\u95F4\u8C0D\u5173\u8FDB\u4E86\u5730\u7262\u3002\n\u66F4\u53EF\u60B2\u7684\u662F\uFF0C\u8FD9\u5EA7\u8981\u585E\u4E5F\u672A\u80FD\u5E78\u514D\u4E8E\u6DF7\u4E71\u3002\u4E0D\u77E5\u662F\u4EC0\u4E48\u5F15\u53D1\u4E86\u5185\u5916\u7684\u52A8\u8361\uFF0C\u58EB\u5175\u4EEC\u4E5F\u653E\u5F03\u4E86\u7BA1\u7406\uFF0C\u8BA9\u6211\u4EEC\u5728\u7262\u4E2D\u81EA\u751F\u81EA\u706D\uFF0C\u6211\u4EEC\u65AD\u6C34\u65AD\u7CAE\uFF0C\u6050\u6015\u662F\u65F6\u65E5\u65E0\u591A\u3002\u6211\u7684\u7231\uFF0C\u5E0C\u671B\u4F60\u6CA1\u6709\u79BB\u5F00\u6211\u4EEC\u7684\u90BB\u5C45\u4E00\u5BB6\uFF0C\u90A3\u5BF9\u59D0\u5F1F\u90FD\u662F\u597D\u4EBA\uFF0C\u59D0\u59D0\u683C\u857E\u4E1D\u7684\u9152\u9986\u5411\u6765\u4FE1\u8A89\u826F\u597D\uFF0C\u5F1F\u5F1F\u79D1\u5C14\u4E5F\u662F\u4E2A\u503C\u5F97\u4FE1\u8D56\u7684\u5C0F\u4F19\uFF0C\u4F46\u613F\u4ED6\u4EEC\u80FD\u62A4\u4F60\u5468\u5168\u3002\n\u5982\u679C\u5947\u8FF9\u53D1\u751F\uFF0C\u6709\u4EBA\u53D1\u73B0\u4E86\u8FD9\u5C01\u4FE1\uFF0C\u8BF7\u628A\u5B83\u5E26\u7ED9\u4F60\uFF0C\u6216\u8005\u662F\u683C\u857E\u4E1D\u548C\u79D1\u5C14\u3002\u613F\u8FD9\u5C01\u4FE1\u80FD\u4F20\u9012\u6211\u7684\u7231\u3002\n\u6C38\u8FDC\u7231\u4F60\u7684
    \u96F7\u7EB3\u5FB7"
  detailedImage: {fileID: 21300000, guid: 3c78a452248f9cc4298d7a53692f329c, type: 3}
  translate: 1
  localKey: "\u8981\u585E\u5F39\u7A9714"
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8400119041305053925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8627571e8985d9f43b17412b0d7a0a15, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemType: "\u7269\u54C1"
  item: {fileID: 11400000, guid: 849eb98ab7b8548488b9cccf7684a594, type: 2}
  weapon: {fileID: 0}
  mark: {fileID: 0}
  card: {fileID: 0}
  armor: {fileID: 0}
  count: 1
  SaveDataName: "\u843D\u96BE\u6751\u6C11\u7684\u4FE1\u4EF6-\u8981\u585E\u5730\u7262"
--- !u!1 &8512535587177189634
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3302158562232237590}
  - component: {fileID: 2291629815519627926}
  - component: {fileID: 5125903219202831981}
  - component: {fileID: 2786190035225615914}
  - component: {fileID: 8054266156352044334}
  - component: {fileID: 2684291435843052570}
  - component: {fileID: 1770497078175151751}
  m_Layer: 0
  m_Name: B
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3302158562232237590
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 34.58252, y: -31.939896, z: 56.244385}
  m_LocalScale: {x: 60, y: 13.7868, z: 14.6516}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2315414970412035}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2291629815519627926
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5125903219202831981
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2786190035225615914
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &8054266156352044334
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a62d8f097316182408722a5d81b59f5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  m_impactDropDown: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  value: 1
--- !u!114 &2684291435843052570
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 1770497078175151751}
--- !u!114 &1770497078175151751
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8512535587177189634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8054266156352044334}
        m_TargetAssemblyTypeName: VariableDictionarySetter, Assembly-CSharp
        m_MethodName: SetVariable
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1001 &2438984394773165198
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3927847338411077117}
    m_Modifications:
    - target: {fileID: 316756465747089182, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 498608103130263934, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977584, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_Name
      value: ItemBase
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977585, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977585, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: useCustomButtonName
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.25513572
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.30031833
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.1
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.14
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977599, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_Radius
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 1705778129800977599, guid: 14fcb57c61699ab43846be5f0847ea3f,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 14fcb57c61699ab43846be5f0847ea3f, type: 3}
--- !u!4 &3924085847414417456 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1705778129800977598, guid: 14fcb57c61699ab43846be5f0847ea3f,
    type: 3}
  m_PrefabInstance: {fileID: 2438984394773165198}
  m_PrefabAsset: {fileID: 0}
