%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &376830578035284970
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8286690368533494855}
  m_Layer: 0
  m_Name: "\u8981\u585E\u5730\u7262\u5B9D\u7BB1"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8286690368533494855
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 376830578035284970}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7837958460391470235}
  - {fileID: 4947698346208266973}
  - {fileID: 3794627167162168447}
  - {fileID: 2980471541776004179}
  - {fileID: 2127070721139411899}
  - {fileID: 4373440573921891074}
  m_Father: {fileID: 0}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &609920130484684417
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3121423852746871447}
  - component: {fileID: 9140577047170996593}
  - component: {fileID: 7309928739059195750}
  - component: {fileID: 5850224091154648150}
  m_Layer: 0
  m_Name: "\u4F20\u9001\u76EE\u7684\u5730"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3121423852746871447
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 609920130484684417}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.906, y: -10.813, z: -13.88145}
  m_LocalScale: {x: 1, y: 0.091409, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2127070721139411899}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &9140577047170996593
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 609920130484684417}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7309928739059195750
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 609920130484684417}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5850224091154648150
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 609920130484684417}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1080087243638005081
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6284520626574157810}
  - component: {fileID: 7919117312406077517}
  - component: {fileID: 7752115815125239861}
  - component: {fileID: 3880653708838725922}
  - component: {fileID: 1159281117045789151}
  m_Layer: 0
  m_Name: Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6284520626574157810
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080087243638005081}
  m_LocalRotation: {x: -0.006814942, y: -0.010243843, z: -0.00006981649, w: 0.9999243}
  m_LocalPosition: {x: -0.164, y: -0.485, z: 0}
  m_LocalScale: {x: 0.1398, y: 0.0385, z: 0.15714663}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4278455527077039524}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: -0.781, y: -1.174, z: 0}
--- !u!33 &7919117312406077517
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080087243638005081}
  m_Mesh: {fileID: 0}
--- !u!23 &7752115815125239861
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080087243638005081}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8569c78a1793fa04b89e6ba58e902681, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &3880653708838725922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080087243638005081}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: e39c32c8bf72e8940bb043f362c0ed06, type: 2}
  initialSkinName: 
  fixPrefabOverrideViaMeshFilter: 2
  initialFlipX: 1
  initialFlipY: 0
  updateWhenInvisible: 3
  separatorSlotNames: []
  zSpacing: -0.0023
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  fixDrawOrder: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  physicsPositionInheritanceFactor: {x: 1, y: 1}
  physicsRotationInheritanceFactor: 1
  physicsMovementRelativeTo: {fileID: 0}
  updateTiming: 1
  unscaledTime: 0
  _animationName: B_idle
  loop: 1
  timeScale: 1
--- !u!114 &1159281117045789151
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080087243638005081}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f21c9538588898a45a3da22bf4779ab3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  rootMotionBoneName: root
  transformPositionX: 1
  transformPositionY: 1
  transformRotation: 0
  rootMotionScaleX: 1
  rootMotionScaleY: 1
  rootMotionScaleRotation: 1
  rootMotionTranslateXPerY: 0
  rootMotionTranslateYPerX: 0
  skeleTransform: {fileID: 0}
  rigidBody2D: {fileID: 0}
  applyRigidbody2DGravity: 0
  rigidBody: {fileID: 0}
  disableOnOverride: 1
  animationTrackFlags: -1
--- !u!1 &1368128282739407581
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4224294921712379894}
  - component: {fileID: 7865169578912166756}
  - component: {fileID: 5058369112888604751}
  - component: {fileID: 4388203799389229936}
  - component: {fileID: 5296710506314002779}
  - component: {fileID: 7117528324694492086}
  - component: {fileID: 6081762778908559096}
  m_Layer: 0
  m_Name: "\u4E8C\u6B21\u8FDB\u5165\u5730\u7262\u68C0\u6D4B"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &4224294921712379894
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.0854, y: 0.439, z: -9.2188}
  m_LocalScale: {x: 2.732155, y: 1, z: 1.6923}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2127070721139411899}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7865169578912166756
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5058369112888604751
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4388203799389229936
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5296710506314002779
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 7117528324694492086}
--- !u!114 &7117528324694492086
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6081762778908559096}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!114 &6081762778908559096
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368128282739407581}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u7ACB\u9752\u4E91\u8981\u585E\u5730\u7262\u6D41\u7A0B"
  m_impactDropDown: "\u7ACB\u9752\u4E91\u8981\u585E\u5730\u7262\u6D41\u7A0B"
  selectedOperator: 2
  comparisonValue: 2
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3862080183040742204}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!1 &3292722855208926238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8195012493183194391}
  - component: {fileID: 4022286598664498161}
  - component: {fileID: 2263839216931590417}
  - component: {fileID: 2808277705275727676}
  - component: {fileID: 7248116700227565798}
  - component: {fileID: 8735179161224140046}
  - component: {fileID: 219006421918151967}
  m_Layer: 0
  m_Name: "\u68C0\u6D4B"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8195012493183194391
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.0719, y: 1.5292, z: -2.4311}
  m_LocalScale: {x: 3.7219503, y: 4.0584, z: 4.1624}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4373440573921891074}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4022286598664498161
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2263839216931590417
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2808277705275727676
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7248116700227565798
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 11
  useButton: 1
  useCustomButtonName: 1
  customButtonName: "\u6253\u5F00"
  interaction_Base:
  - {fileID: 8735179161224140046}
--- !u!114 &8735179161224140046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3ee50e58c700c9408bbd9d238cb6706, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  item: {fileID: 11400000, guid: 30694ec891bc3594d90a24bce88b438f, type: 2}
  checkhave: 1
  checkcount: 0
  count: 1
  OperatorIndex: 
  True:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 4573118941263032141}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3894270468117404227}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 219006421918151967}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  False:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &219006421918151967
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3292722855208926238}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84a67533a243a244e8191abfb37ef0d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SaveName: "\u7ACB\u9752\u4E91\u5F00\u95E8\u68C0\u6D4B\u901A\u8FC7"
  StateA:
  - gameObject: {fileID: 3292722855208926238}
    Active: 0
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 0
    rotation: {x: 0, y: 0, z: 0, w: 0}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  StateB: []
  ChangeDirect: 1
--- !u!1 &3668605295742388575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9135629064778863732}
  - component: {fileID: 3170286735851513772}
  - component: {fileID: 8812006082891919505}
  - component: {fileID: 8791391172318360026}
  - component: {fileID: 7901632872275695143}
  - component: {fileID: 7036411956922868267}
  - component: {fileID: 6764222760314181297}
  - component: {fileID: 5300054972657341258}
  - component: {fileID: 873620728770535696}
  - component: {fileID: 1351480381340121867}
  - component: {fileID: 7166226548562151451}
  m_Layer: 0
  m_Name: "\u4F20\u9001\u9677\u9631"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9135629064778863732
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.1564, y: 0, z: 0.2087}
  m_LocalScale: {x: 2.568843, y: 1, z: 3.673699}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2127070721139411899}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3170286735851513772
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8812006082891919505
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8791391172318360026
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7901632872275695143
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 1
  useCustomButtonName: 1
  customButtonName: "\u6253\u5F00"
  interaction_Base:
  - {fileID: 7036411956922868267}
--- !u!114 &7036411956922868267
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6764222760314181297}
        m_TargetAssemblyTypeName: Event_TeleportToTransform, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 5300054972657341258}
        m_TargetAssemblyTypeName: Temp_SimpleAttention, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 873620728770535696}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 1351480381340121867}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 7166226548562151451}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!114 &6764222760314181297
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2480e55c15faebe4f87e5e8091c245cf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetTransform: {fileID: 3121423852746871447}
--- !u!114 &5300054972657341258
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bd8cc6730391e0d44b62922b568a0cde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  simpleLog: "\u4F60\u88AB\u6254\u4E86\u51FA\u53BB\u2026\u2026"
  translate: 1
  localKey: "\u8981\u585E\u63D0\u793A6"
--- !u!114 &873620728770535696
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84a67533a243a244e8191abfb37ef0d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SaveName: "\u5730\u7262\u7ACB\u9752\u4E912"
  StateA:
  - gameObject: {fileID: 7483461981749464639}
    Active: 0
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 0
    rotation: {x: 0, y: 0, z: 0, w: 0}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  StateB: []
  ChangeDirect: 1
--- !u!114 &1351480381340121867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u7ACB\u9752\u4E91\u8981\u585E\u5730\u7262\u6D41\u7A0B"
  m_impactDropDown: "\u7ACB\u9752\u4E91\u8981\u585E\u5730\u7262\u6D41\u7A0B"
  selectedOperator: 1
  comparisonValue: 2
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6636332952884017314}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
--- !u!114 &7166226548562151451
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3668605295742388575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: 
  m_impactDropDown: 
  selectedOperator: 4
  comparisonValue: 2
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3862080183040742204}
        m_TargetAssemblyTypeName: Event_Switcher, Assembly-CSharp
        m_MethodName: SwitchA
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!1 &4521307544850948856
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5977660895406425781}
  - component: {fileID: 2003126959738523000}
  - component: {fileID: 3381929904432310290}
  - component: {fileID: 1120465047111155522}
  - component: {fileID: 5390331808877382283}
  - component: {fileID: 8885211978936355388}
  - component: {fileID: 3894270468117404227}
  m_Layer: 0
  m_Name: "\u963B\u62E6\u5BF9\u8BDD"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5977660895406425781
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.95, y: 1.2623, z: -4.6443}
  m_LocalScale: {x: 1, y: 3.5245, z: 10.2886}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4373440573921891074}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2003126959738523000
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3381929904432310290
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1120465047111155522
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5390331808877382283
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 0
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 8885211978936355388}
--- !u!114 &8885211978936355388
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b343d007a95bbac48a02248358dbf8fc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  dialogueFile: {fileID: 11400000, guid: e8983536790215f40a8510921e1b86ca, type: 2}
--- !u!114 &3894270468117404227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4521307544850948856}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84a67533a243a244e8191abfb37ef0d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SaveName: "\u963B\u62E6\u5BF9\u8BDD\u9690\u85CF"
  StateA:
  - gameObject: {fileID: 4521307544850948856}
    Active: 0
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 0
    rotation: {x: 0, y: 0, z: 0, w: 0}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  StateB: []
  ChangeDirect: 1
--- !u!1 &6636332952884017314
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4278455527077039524}
  - component: {fileID: 3266661579664553350}
  - component: {fileID: 796334760656051222}
  - component: {fileID: 7132854798191184441}
  - component: {fileID: 2521656001268470560}
  - component: {fileID: 6683918517763404889}
  - component: {fileID: 4852760518284978302}
  - component: {fileID: 8768985082961230894}
  - component: {fileID: 3862080183040742204}
  m_Layer: 0
  m_Name: "\u7ACB\u9752\u4E91\u5BF9\u8BDD2"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &4278455527077039524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.896, y: -9.861, z: -17.162}
  m_LocalScale: {x: 0.47724953, y: 2, z: 0.7406}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6284520626574157810}
  m_Father: {fileID: 2127070721139411899}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3266661579664553350
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &796334760656051222
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7132854798191184441
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 4.4596334, y: 1, z: 4.216147}
  m_Center: {x: 1.0654305, y: 0, z: 1.348279}
--- !u!114 &2521656001268470560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 1
  useCustomButtonName: 1
  customButtonName: "\u5BF9\u8BDD"
  interaction_Base:
  - {fileID: 6683918517763404889}
  - {fileID: 4852760518284978302}
--- !u!114 &6683918517763404889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b343d007a95bbac48a02248358dbf8fc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  dialogueFile: {fileID: 11400000, guid: ec8b9a97b08409f48ab4820718dd9206, type: 2}
--- !u!114 &4852760518284978302
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8768985082961230894}
        m_TargetAssemblyTypeName: SpineAnimationPlayer, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!114 &8768985082961230894
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a05e227012c905a4c9d14b4e496a0dbe, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonAnimation: {fileID: 3880653708838725922}
  startAnimation: click_01
  endAnimation: B_idle
--- !u!114 &3862080183040742204
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6636332952884017314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84a67533a243a244e8191abfb37ef0d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SaveName: "\u7ACB\u9752\u4E91\u5BF9\u8BDD2\u5B8C\u6210"
  StateA:
  - gameObject: {fileID: 6636332952884017314}
    Active: 0
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 0
    rotation: {x: 0, y: 0, z: 0, w: 0}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  StateB: []
  ChangeDirect: 1
--- !u!1001 &145787160019613489
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8286690368533494855}
    m_Modifications:
    - target: {fileID: 3456790631305093632, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_Name
      value: "\u5B9D\u7BB1-\u6750\u6599\u5E7D\u6697\u82F1\u77F3*2"
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -671.04
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.28
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2380.84
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7213854772306101690, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveName
      value: "\u8981\u585E\u5730\u7262-\u5E7D\u6697\u82F1\u77F3"
      objectReference: {fileID: 0}
    - target: {fileID: 8605595443640959356, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: item
      value: 
      objectReference: {fileID: 11400000, guid: c786eec68f3a54343a64a7a5ccce436b,
        type: 2}
    - target: {fileID: 8605595443640959356, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: count
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8605595443640959356, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveDataName
      value: "\u5B9D\u7BB1-\u6750\u6599\u5E7D\u6697\u82F1\u77F3*2"
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 01f1180742bb53544a58d4bd89e75651, type: 3}
--- !u!4 &4947698346208266973 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 145787160019613489}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2238620247571694523
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8286690368533494855}
    m_Modifications:
    - target: {fileID: 645884475836275518, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1802177488603840648, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_Name
      value: "\u7ACB\u9752\u4E91\u5F00\u95E8\u673A\u5173"
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -808.5609
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.1277103
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2384.3638
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3.4500868
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 2.9964
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.1015
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.0003
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3373194550799054661, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects:
    - {fileID: 6962396162020953776, guid: 9daabfdc93c0e0f41baeebd23e82e53e, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 8195012493183194391}
    - targetCorrespondingSourceObject: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 5977660895406425781}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 606729385633108995, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4573118941263032141}
  m_SourcePrefab: {fileID: 100100000, guid: 9daabfdc93c0e0f41baeebd23e82e53e, type: 3}
--- !u!1 &1691845377737742264 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 606729385633108995, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
    type: 3}
  m_PrefabInstance: {fileID: 2238620247571694523}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &4573118941263032141
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691845377737742264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84a67533a243a244e8191abfb37ef0d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SaveName: "\u7ACB\u9752\u4E91\u7262\u623F\u5F00\u95E8"
  StateA:
  - gameObject: {fileID: 1691845377737742264}
    Active: 1
    changePosition: 0
    position: {x: 0, y: 0, z: 0}
    changeRotation: 1
    rotation: {x: 0, y: 0.551937, z: 0, w: 0.83388585}
    changeScale: 0
    scale: {x: 0, y: 0, z: 0}
    playAnimation: 0
    animationClip: {fileID: 0}
    loop: 0
    changeAnimatorBoolParameter: 0
    animatorBoolParameterName: 
    animatorBoolParameterValue: 0
  StateB: []
  ChangeDirect: 1
--- !u!4 &4373440573921891074 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2567254402371358905, guid: 9daabfdc93c0e0f41baeebd23e82e53e,
    type: 3}
  m_PrefabInstance: {fileID: 2238620247571694523}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3056312993628719479
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8286690368533494855}
    m_Modifications:
    - target: {fileID: 2066636768355321414, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: interaction_Base.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2066636768355321414, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: interaction_Base.Array.data[1]
      value: 
      objectReference: {fileID: 7632760485936117808}
    - target: {fileID: 2066636768355321415, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3456790631305093632, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_Name
      value: "\u5B9D\u7BB1-\u5927\u793C\u53053\u6C24\u6C325\u5F26\u6708"
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 2.6810517
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 2.4053783
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 2.4053783
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -764.49
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -41.99
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2398.44
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7213854772306101690, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveName
      value: "\u8981\u585E\u5730\u7262-\u8F89\u5149\u793C\u5305"
      objectReference: {fileID: 0}
    - target: {fileID: 7918873493788940475, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: item
      value: 
      objectReference: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
        type: 2}
    - target: {fileID: 7918873493788940475, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: count
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 7918873493788940475, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveDataName
      value: "\u5730\u7262-\u6C24\u6C32\u8F89\u5149"
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 2066636768355321415, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 7632760485936117808}
  m_SourcePrefab: {fileID: 100100000, guid: 01f1180742bb53544a58d4bd89e75651, type: 3}
--- !u!1 &3946313573926571824 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2066636768355321415, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 3056312993628719479}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7632760485936117808
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3946313573926571824}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8627571e8985d9f43b17412b0d7a0a15, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemType: "\u7269\u54C1"
  item: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72, type: 2}
  weapon: {fileID: 0}
  mark: {fileID: 0}
  card: {fileID: 0}
  armor: {fileID: 0}
  count: 5
  SaveDataName: "\u5730\u7262-\u5F26\u6708\u8F89\u5149"
--- !u!4 &7837958460391470235 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 3056312993628719479}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &*******************
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8286690368533494855}
    m_Modifications:
    - target: {fileID: 596512418896678709, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_Name
      value: "\u5B9D\u7BB1\u602A"
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -799.8
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.23
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2402.62
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4255230592122784847, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: MonsterList.Array.data[0].monsterLevel
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4255230592122784847, guid: 18b32ceed64afbf4a931fc82f186919b,
        type: 3}
      propertyPath: MonsterList.Array.data[0].MonsterPrefab
      value: 
      objectReference: {fileID: 3283760461818085518, guid: 53e66689253af8340a9e622909126fe4,
        type: 3}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 18b32ceed64afbf4a931fc82f186919b, type: 3}
--- !u!4 &2980471541776004179 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1279176575887300663, guid: 18b32ceed64afbf4a931fc82f186919b,
    type: 3}
  m_PrefabInstance: {fileID: *******************}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5633873399440053448
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2127070721139411899}
    m_Modifications:
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.37298796
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.41573504
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.41573504
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 285.10382
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 6.766504
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -1007.775
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2123369541759978606, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -815.9
      objectReference: {fileID: 0}
    - target: {fileID: 3023343633344813815, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_Name
      value: "\u7ACB\u9752\u4E91\u8981\u585E\u5730\u72621"
      objectReference: {fileID: 0}
    - target: {fileID: 3023343633344813815, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3972911053615550912, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.1775
      objectReference: {fileID: 0}
    - target: {fileID: 3972911053615550912, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.1798
      objectReference: {fileID: 0}
    - target: {fileID: 3972911053615550912, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -811.74
      objectReference: {fileID: 0}
    - target: {fileID: 3972911053615550912, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.38
      objectReference: {fileID: 0}
    - target: {fileID: 3972911053615550912, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2385.93
      objectReference: {fileID: 0}
    - target: {fileID: 6725645036157121393, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_Mesh
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6850102005914659903, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: useButton
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7758700149269776433, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_Size.x
      value: 7.3157225
      objectReference: {fileID: 0}
    - target: {fileID: 7758700149269776433, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_Size.z
      value: 4.711532
      objectReference: {fileID: 0}
    - target: {fileID: 7758700149269776433, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_Center.x
      value: 3.1578612
      objectReference: {fileID: 0}
    - target: {fileID: 7758700149269776433, guid: d5c99a76c730d2a48adf45a409b81547,
        type: 3}
      propertyPath: m_Center.z
      value: 1.3171623
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d5c99a76c730d2a48adf45a409b81547, type: 3}
--- !u!4 &5157172754993082682 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 702138548283434482, guid: d5c99a76c730d2a48adf45a409b81547,
    type: 3}
  m_PrefabInstance: {fileID: 5633873399440053448}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &7483461981749464639 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3023343633344813815, guid: d5c99a76c730d2a48adf45a409b81547,
    type: 3}
  m_PrefabInstance: {fileID: 5633873399440053448}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6568762040295765591
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8286690368533494855}
    m_Modifications:
    - target: {fileID: 1101573579202166303, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2066636768355321415, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3435582765087434296, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3435582765087434296, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3435582765087434296, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3435582765087434296, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3456790631305093632, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_Name
      value: "\u5B9D\u7BB1-\u7ACB\u9752\u4E91\u6076\u4F5C\u5267"
      objectReference: {fileID: 0}
    - target: {fileID: 4288141355212558199, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4288141355212558199, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.000000021855694
      objectReference: {fileID: 0}
    - target: {fileID: 4288141355212558199, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.00000004371139
      objectReference: {fileID: 0}
    - target: {fileID: 4288141355212558199, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 1.0165751e-15
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -764.37805
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -16.276001
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2424.08
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: endValueV3.x
      value: 45
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: endValueV3.y
      value: 45
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: endValueV3.z
      value: 45
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: optionalInt0
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: animationType
      value: 13
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: optionalBool1
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5592585108269431347, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: optionalFloat0
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 6239007963214672486, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: 0
      addedObject: {fileID: 5157172754993082682}
    - targetCorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 9135629064778863732}
    - targetCorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 3121423852746871447}
    - targetCorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4278455527077039524}
    - targetCorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4224294921712379894}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3515439370207958477, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 5780133727869606441}
  m_SourcePrefab: {fileID: 100100000, guid: 01f1180742bb53544a58d4bd89e75651, type: 3}
--- !u!4 &2127070721139411899 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 6568762040295765591}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &6965493650612386080 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4288141355212558199, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 6568762040295765591}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &7773695911205348250 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3515439370207958477, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 6568762040295765591}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &5780133727869606441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7773695911205348250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d0390bd8b8ffd640b34fe25065ff1df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  updateType: 0
  isSpeedBased: 0
  hasOnStart: 0
  hasOnPlay: 0
  hasOnUpdate: 0
  hasOnStepComplete: 0
  hasOnComplete: 0
  hasOnTweenCreated: 0
  hasOnRewind: 0
  onStart:
    m_PersistentCalls:
      m_Calls: []
  onPlay:
    m_PersistentCalls:
      m_Calls: []
  onUpdate:
    m_PersistentCalls:
      m_Calls: []
  onStepComplete:
    m_PersistentCalls:
      m_Calls: []
  onComplete:
    m_PersistentCalls:
      m_Calls: []
  onTweenCreated:
    m_PersistentCalls:
      m_Calls: []
  onRewind:
    m_PersistentCalls:
      m_Calls: []
  targetIsSelf: 1
  targetGO: {fileID: 0}
  tweenTargetIsTargetGO: 1
  delay: 0
  duration: 0.6
  easeType: 6
  easeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  loopType: 0
  loops: 1
  id: 
  isRelative: 0
  isFrom: 0
  isIndependentUpdate: 0
  autoKill: 0
  autoGenerate: 1
  isActive: 1
  isValid: 1
  target: {fileID: 6965493650612386080}
  animationType: 13
  targetType: 11
  forcedTargetType: 0
  autoPlay: 0
  useTargetAsV3: 0
  endValueFloat: 0
  endValueV3: {x: 45, y: 45, z: 45}
  endValueV2: {x: 0, y: 0}
  endValueColor: {r: 1, g: 1, b: 1, a: 1}
  endValueString: 
  endValueRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 0
    height: 0
  endValueTransform: {fileID: 0}
  optionalBool0: 0
  optionalBool1: 1
  optionalFloat0: 90
  optionalInt0: 10
  optionalRotationMode: 0
  optionalScrambleMode: 0
  optionalShakeRandomnessMode: 0
  optionalString: 
--- !u!1001 &8215982462564518291
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8286690368533494855}
    m_Modifications:
    - target: {fileID: 3456790631305093632, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_Name
      value: "\u5B9D\u7BB1-\u6750\u6599\u6B8B\u7834\u5377\u8F74*2"
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -699.74
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -42.42
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2381.46
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7213854772306101690, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveName
      value: "\u8981\u585E\u5730\u7262-\u6B8B\u7834\u5377\u8F74"
      objectReference: {fileID: 0}
    - target: {fileID: 7918873493788940475, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: item
      value: 
      objectReference: {fileID: 11400000, guid: a110a526963d7b2429e6871833e549c2,
        type: 2}
    - target: {fileID: 7918873493788940475, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: count
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7918873493788940475, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveDataName
      value: "\u8981\u585E\u5730\u7262\u5B9D\u7BB1--\u6B8B\u7834\u5377\u8F74*2"
      objectReference: {fileID: 0}
    - target: {fileID: 8605595443640959356, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: item
      value: 
      objectReference: {fileID: 11400000, guid: a110a526963d7b2429e6871833e549c2,
        type: 2}
    - target: {fileID: 8605595443640959356, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: count
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8605595443640959356, guid: 01f1180742bb53544a58d4bd89e75651,
        type: 3}
      propertyPath: SaveDataName
      value: "\u5B9D\u7BB1-\u6750\u6599\u6B8B\u7834\u5377\u8F74*2"
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 01f1180742bb53544a58d4bd89e75651, type: 3}
--- !u!4 &3794627167162168447 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5092500016937844204, guid: 01f1180742bb53544a58d4bd89e75651,
    type: 3}
  m_PrefabInstance: {fileID: 8215982462564518291}
  m_PrefabAsset: {fileID: 0}
