---
description: 
globs: Scripts 目录结构
alwaysApply: false
---
# Scripts 目录结构分析 ([Assets/Scripts](mdc:Assets/Scripts))

此规则概述了 `Assets/Scripts` 目录下的主要文件夹和文件的功能，帮助理解项目代码结构。

## 主要目录

*   **[UI/](mdc:Assets/Scripts/UI)**: 包含用户界面 (UI) 相关的脚本，例如窗口、按钮、HUD 等。
*   **[Battle/](mdc:Assets/Scripts/Battle)**: 包含战斗系统相关的脚本，如战斗逻辑、技能、敌人 AI 等。
*   **[Volume/](mdc:Assets/Scripts/Volume)**: 可能包含与 Unity 的 Volume 系统（如 Post-processing Volumes 或 Audio Volumes）相关的脚本。
*   **[Level/](mdc:Assets/Scripts/Level)**: 包含关卡管理、关卡逻辑或程序化生成相关的脚本。
*   **[Attribute/](mdc:Assets/Scripts/Attribute)**: 包含定义角色、物品或其他实体的属性（如生命值、攻击力）的脚本。
*   **[Tools/](mdc:Assets/Scripts/Tools)**: 包含编辑器扩展、辅助工具或其他开发工具脚本。
*   **[Network/](mdc:Assets/Scripts/Network)**: 包含网络功能相关的脚本，可能用于多人游戏或与服务器通信。
*   **[Camera/](mdc:Assets/Scripts/Camera)**: 包含摄像机控制、行为或效果相关的脚本。
*   **[FluentAPI/](mdc:Assets/Scripts/FluentAPI)**: 可能包含使用 Fluent Interface 设计模式实现的 API。
*   **[BehaviorTree/](mdc:Assets/Scripts/BehaviorTree)**: 包含行为树实现，常用于 AI 逻辑。
*   **[Explore/](mdc:Assets/Scripts/Explore)**: 包含游戏探索玩法相关的脚本。
*   **[MainActor/](mdc:Assets/Scripts/MainActor)**: 包含主角（玩家角色）相关的特定脚本。
*   **[GM/](mdc:Assets/Scripts/GM)**: 包含游戏管理员 (Game Master) 功能或调试工具的脚本。
*   **[Excel/](mdc:Assets/Scripts/Excel)**: 包含读写 Excel 文件相关的脚本，通常用于管理游戏数据。
*   **[Job/](mdc:Assets/Scripts/Job)**: 可能包含与 Unity Job System 或自定义任务系统相关的脚本。
*   **[WorldMessage/](mdc:Assets/Scripts/WorldMessage)**: 包含在游戏世界中显示消息或通知的系统脚本。
*   **[Dialogue/](mdc:Assets/Scripts/Dialogue)**: 包含对话系统相关的脚本。
*   **[Scenes/](mdc:Assets/Scripts/Scenes)**: 包含场景管理或特定场景逻辑的脚本。
*   **[SaveData/](mdc:Assets/Scripts/SaveData)**: 包含游戏存档和读档功能的脚本。
*   **[Spine/](mdc:Assets/Scripts/Spine)**: 包含与 Spine 2D 骨骼动画软件集成相关的脚本。
*   **[Novice/](mdc:Assets/Scripts/Novice)**: 包含新手引导或教程相关的脚本。
*   **[Animation/](mdc:Assets/Scripts/Animation)**: 包含通用动画控制脚本，可能与 Mecanim 或其他动画系统相关。
*   **[UnityEvent/](mdc:Assets/Scripts/UnityEvent)**: 包含使用或扩展 UnityEvent 的脚本。
*   **[CSV/](mdc:Assets/Scripts/CSV)**: 包含读写 CSV 文件相关的脚本，作为数据管理的另一种方式。
*   **[Localization/](mdc:Assets/Scripts/Localization)**: 包含处理游戏本地化（多语言）功能的脚本。
*   **[InteractionActor/](mdc:Assets/Scripts/InteractionActor)**: 包含可交互对象或 NPC 相关的脚本。
*   **[Timeline/](mdc:Assets/Scripts/Timeline)**: 包含与 Unity Timeline 功能集成的脚本。
*   **[Steamworks.NET/](mdc:Assets/Scripts/Steamworks.NET)**: 包含与 Steamworks API 集成相关的脚本。
*   **[DataSO/](mdc:Assets/Scripts/DataSO)**: 包含使用 ScriptableObject 存储游戏数据的脚本。
*   **[Light/](mdc:Assets/Scripts/Light)**: 包含灯光控制或效果相关的脚本。
*   **[RenderTexture/](mdc:Assets/Scripts/RenderTexture)**: 包含使用 Render Texture 功能的脚本。
*   **[Audios/](mdc:Assets/Scripts/Audios)**: 包含音频播放、管理或效果相关的脚本。

## 根目录下的重要文件

*   **[DataCollection.cs](mdc:Assets/Scripts/DataCollection.cs)**: 可能用于收集游戏数据或分析。
*   **[MainActorManager.cs](mdc:Assets/Scripts/MainActorManager.cs)**: 管理主角实例和状态。
*   **[CardBase.cs](mdc:Assets/Scripts/CardBase.cs)**: 卡牌系统的基础类。
*   **[DataImportSave.cs](mdc:Assets/Scripts/DataImportSave.cs)**: 处理数据的导入和保存逻辑。
*   **[VariableDictionaryManager.cs](mdc:Assets/Scripts/VariableDictionaryManager.cs)**: 管理全局变量或键值对数据。
*   **[WeaponDetails.cs](mdc:Assets/Scripts/WeaponDetails.cs)**: 定义武器属性和行为。
*   **[GameObjectManager.cs](mdc:Assets/Scripts/GameObjectManager.cs)**: 管理场景中的 GameObject，可能包含对象池。
*   **[Unit.cs](mdc:Assets/Scripts/Unit.cs)**: 代表游戏中的一个单位（角色、敌人等）的基础类。
*   **[VideoController.cs](mdc:Assets/Scripts/VideoController.cs)**: 控制视频播放。
*   **[ItemDictionary.cs](mdc:Assets/Scripts/ItemDictionary.cs)**: 管理游戏中的物品数据。
*   **[AreaInfo.cs](mdc:Assets/Scripts/AreaInfo.cs)** / **[AreaInfoData.cs](mdc:Assets/Scripts/AreaInfoData.cs)**: 定义和管理游戏区域信息。
*   **[FogController.cs](mdc:Assets/Scripts/FogController.cs)**: 控制战争迷雾或其他雾效。
*   **[OverlayVideoController.cs](mdc:Assets/Scripts/OverlayVideoController.cs)**: 控制覆盖式视频播放（如过场动画）。
*   **[MainDictionary.cs](mdc:Assets/Scripts/MainDictionary.cs)**: 可能是一个核心的数据字典或管理器。
*   **[PlayerState.cs](mdc:Assets/Scripts/PlayerState.cs)**: 管理玩家的状态信息。
*   **[FeelPlayerManager.cs](mdc:Assets/Scripts/FeelPlayerManager.cs)**: 可能管理游戏“手感”或反馈效果。
*   **[ActorMovement.cs](mdc:Assets/Scripts/ActorMovement.cs)**: 处理角色的移动逻辑。
*   **[MouseView.cs](mdc:Assets/Scripts/MouseView.cs)**: 处理基于鼠标输入的外观。
*   **[QualitySettingsManager.cs](mdc:Assets/Scripts/QualitySettingsManager.cs)**: 管理游戏画面质量设置。
*   **[EquipmentDetails.cs](mdc:Assets/Scripts/EquipmentDetails.cs)**: 定义装备的属性和行为。
*   **[GameManager.cs](mdc:Assets/Scripts/GameManager.cs)**: 核心游戏流程管理器，通常是单例。
*   **[TipsDictionary.cs](mdc:Assets/Scripts/TipsDictionary.cs)**: 管理游戏提示信息。
*   **[ArtResouceDictionary.cs](mdc:Assets/Scripts/ArtResouceDictionary.cs)**: 管理美术资源引用。
*   **[ObjectPool.cs](mdc:Assets/Scripts/ObjectPool.cs)**: 提供对象池功能以优化性能。
*   **[CardDictionary.cs](mdc:Assets/Scripts/CardDictionary.cs)**: 管理卡牌数据。
*   **[ItemSynthesisDictionary.cs](mdc:Assets/Scripts/ItemSynthesisDictionary.cs)**: 管理物品合成配方。
*   **[TradeDictionary.cs](mdc:Assets/Scripts/TradeDictionary.cs)**: 管理交易或商店数据。

此规则文件旨在提供 `Assets/Scripts` 目录的高级概览。具体实现细节需要查看各个文件和子目录的内容。
