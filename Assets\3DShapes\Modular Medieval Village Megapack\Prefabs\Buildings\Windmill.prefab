%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &103691715853461140
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4640775638103698599}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4640775638103698599
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103691715853461140}
  m_LocalRotation: {x: -0.000000019015257, y: -0.49298692, z: 0.0000000107745715,
    w: 0.8700367}
  m_LocalPosition: {x: -2.3902614, y: 11.65, z: -0.7009035}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1431082456193842977}
  - {fileID: 8059877316471258679}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 37
  m_LocalEulerAnglesHint: {x: 0, y: -59.074, z: 0}
--- !u!1 &262368555481342974
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5845760967674957729}
  - component: {fileID: 7164421115923710411}
  - component: {fileID: 37768521316780275}
  - component: {fileID: 5516307184802092050}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5845760967674957729
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 262368555481342974}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4515221368256528461}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7164421115923710411
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 262368555481342974}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &37768521316780275
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 262368555481342974}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5516307184802092050
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 262368555481342974}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079265, y: 3.011355, z: 0.31099522}
  m_Center: {x: 0.11967451, y: 1.4968746, z: 0.0008885642}
--- !u!1 &440809256385435912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1579540787178475890}
  - component: {fileID: 6802237250411340512}
  - component: {fileID: 301188100025123085}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1579540787178475890
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440809256385435912}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 545922097178437189}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6802237250411340512
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440809256385435912}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &301188100025123085
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 440809256385435912}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &443454735274239100
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6602890253830961865}
  - component: {fileID: 8889145871997977473}
  - component: {fileID: 2563855119546511340}
  - component: {fileID: 4502557157620867018}
  m_Layer: 0
  m_Name: Wall3_CW1 (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6602890253830961865
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443454735274239100}
  m_LocalRotation: {x: -0.0000000108108695, y: 0.86909354, z: -0.000000018994642,
    w: 0.49464774}
  m_LocalPosition: {x: 2.0819995, y: 7.155, z: -3.7000008}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 45
  m_LocalEulerAnglesHint: {x: 0, y: 120.707, z: 0}
--- !u!33 &8889145871997977473
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443454735274239100}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &2563855119546511340
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443454735274239100}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4502557157620867018
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 443454735274239100}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079243, y: 3.0155451, z: 0.310995}
  m_Center: {x: 0.11453646, y: 1.5006055, z: 0.0008880719}
--- !u!1 &551000451958248098
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7334811720957015312}
  - component: {fileID: 4166444070585021268}
  - component: {fileID: 423268024321789592}
  - component: {fileID: 7820029729543475541}
  m_Layer: 0
  m_Name: Wood_Fence2 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7334811720957015312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 551000451958248098}
  m_LocalRotation: {x: -0.000000018879541, y: 0.5037885, z: -0.000000011010646, w: 0.8638271}
  m_LocalPosition: {x: 2.5701985, y: 4.161, z: 0.64435726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 50
  m_LocalEulerAnglesHint: {x: 0, y: 60.502, z: 0}
--- !u!33 &4166444070585021268
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 551000451958248098}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &423268024321789592
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 551000451958248098}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7820029729543475541
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 551000451958248098}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607018}
  m_Center: {x: -0.0011021496, y: 0.42235386, z: -0.0026085752}
--- !u!1 &649384593945325935
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1676794121581795614}
  - component: {fileID: 852522140453072834}
  - component: {fileID: 1044198892808390116}
  m_Layer: 0
  m_Name: Wood_Suport1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1676794121581795614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 649384593945325935}
  m_LocalRotation: {x: -0.000000021011552, y: -0.2752365, z: 0.0000000060154837, w: 0.96137655}
  m_LocalPosition: {x: -1.78, y: 3.8969, z: 0.672}
  m_LocalScale: {x: 0.8851, y: 0.8851, z: 0.8851}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 73
  m_LocalEulerAnglesHint: {x: 0, y: -31.952, z: 0}
--- !u!33 &852522140453072834
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 649384593945325935}
  m_Mesh: {fileID: 2441783984478029798, guid: 4590fc541dac1874a9ee25a7a0b25a88, type: 3}
--- !u!23 &1044198892808390116
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 649384593945325935}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37cd02b95341e4149b25001484bfd014, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &831484714332388517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3878186436841578867}
  - component: {fileID: 1713650003122178577}
  - component: {fileID: 2069009362641131089}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3878186436841578867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 831484714332388517}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3160055184589488195}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1713650003122178577
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 831484714332388517}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &2069009362641131089
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 831484714332388517}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &854284122873277666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1306587948705011145}
  - component: {fileID: 5035497132687679636}
  - component: {fileID: 1770690397606391630}
  - component: {fileID: 2419487198097375261}
  m_Layer: 0
  m_Name: Water_Cube1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1306587948705011145
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 854284122873277666}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 1.4919999, y: 2.163, z: -3.357}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5035497132687679636
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 854284122873277666}
  m_Mesh: {fileID: -8104962347539879784, guid: fe820364d58136044817d2aad8a6e2ec, type: 3}
--- !u!23 &1770690397606391630
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 854284122873277666}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 34e5eb34258c34f4183e7c4e5a0cc837, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &2419487198097375261
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 854284122873277666}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.25902927
  m_Height: 0.36578286
  m_Direction: 1
  m_Center: {x: -0.00041234493, y: 0.18068913, z: 0.00038674474}
--- !u!1 &871275901210664653
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7610069153945999192}
  - component: {fileID: 7646278529745125687}
  - component: {fileID: 2962761889240542033}
  - component: {fileID: 5733791090852434509}
  m_Layer: 0
  m_Name: Flute_bread1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7610069153945999192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871275901210664653}
  m_LocalRotation: {x: 0.45658484, y: -0.054854646, z: -0.03306813, w: 0.8873713}
  m_LocalPosition: {x: -2.0066, y: 1.4822, z: 0.003600112}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 53.774, y: -12.464, z: -10.607}
--- !u!33 &7646278529745125687
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871275901210664653}
  m_Mesh: {fileID: 6722308711860000471, guid: 67eaf1dfecaa7b44fbcdef03840ad86e, type: 3}
--- !u!23 &2962761889240542033
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871275901210664653}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d78cc31efe083d049998c311e85be106, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5733791090852434509
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871275901210664653}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.16938232, y: 0.09765358, z: 0.41344365}
  m_Center: {x: -0.00010209518, y: 0.0083088, z: -0.00000008940697}
--- !u!1 &875658118436011622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4859266332465925369}
  - component: {fileID: 7209045604456717729}
  - component: {fileID: 633987140471871573}
  - component: {fileID: 5632263814242646732}
  m_Layer: 0
  m_Name: Barrel (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4859266332465925369
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 875658118436011622}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 1.2879997, y: 1.3879999, z: -4.002}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 91
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7209045604456717729
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 875658118436011622}
  m_Mesh: {fileID: 8108563764783910780, guid: b296b627c932b3a45b5a5088d224ce88, type: 3}
--- !u!23 &633987140471871573
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 875658118436011622}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 12bd68aa68eb95c4c8be8c02d32b5dcf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &5632263814242646732
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 875658118436011622}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.50353736
  m_Height: 1.1603228
  m_Direction: 1
  m_Center: {x: -0.00000014901161, y: 0.580162, z: 0.000605464}
--- !u!1 &973409687815397210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2958477326508290203}
  - component: {fileID: 3925765189265724665}
  - component: {fileID: 319691296031959988}
  - component: {fileID: 5319239737302571727}
  m_Layer: 0
  m_Name: Wood_Fence2 (20)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2958477326508290203
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973409687815397210}
  m_LocalRotation: {x: -0.000000011025913, y: -0.005188786, z: -0.00000001887063,
    w: 0.9999866}
  m_LocalPosition: {x: 0.25224322, y: 7.1483, z: -3.4218075}
  m_LocalScale: {x: 1.1008, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 82
  m_LocalEulerAnglesHint: {x: 0, y: -0.595, z: 0}
--- !u!33 &3925765189265724665
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973409687815397210}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &319691296031959988
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973409687815397210}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5319239737302571727
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973409687815397210}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011021943, y: 0.42235386, z: -0.002608575}
--- !u!1 &1036937731929329504
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6748324782644511564}
  - component: {fileID: 3099941459817487770}
  - component: {fileID: 814544502805889646}
  - component: {fileID: 4306303541646091496}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6748324782644511564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036937731929329504}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8377976602938810851}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3099941459817487770
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036937731929329504}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &814544502805889646
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036937731929329504}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4306303541646091496
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036937731929329504}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007925, y: 3.011355, z: 0.31099507}
  m_Center: {x: 0.1196744, y: 1.4968741, z: 0.00088796776}
--- !u!1 &1055865608269203578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4853823847471940021}
  - component: {fileID: 2446559727349338752}
  - component: {fileID: 3200116430066732001}
  - component: {fileID: 6293222294576308352}
  m_Layer: 0
  m_Name: Wall1_SW1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4853823847471940021
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1055865608269203578}
  m_LocalRotation: {x: -0.000000019013074, y: -0.49316308, z: 0.00000001077842, w: 0.8699369}
  m_LocalPosition: {x: -2.265, y: 1.2400002, z: -1.1059997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 25
  m_LocalEulerAnglesHint: {x: 0, y: -59.097, z: 0}
--- !u!33 &2446559727349338752
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1055865608269203578}
  m_Mesh: {fileID: 6375708778553226300, guid: 6f9a1c867ec13d8429a746ad701bd914, type: 3}
--- !u!23 &3200116430066732001
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1055865608269203578}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ce0d49ee954d64241908acdf028a06ab, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6293222294576308352
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1055865608269203578}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.9925401, y: 3.011355, z: 0.31099504}
  m_Center: {x: 0.12261391, y: 1.5053194, z: 0.00088810176}
--- !u!1 &1075647434011734032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4397528442804326718}
  - component: {fileID: 3941296933878034075}
  - component: {fileID: 8531807458618777853}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4397528442804326718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1075647434011734032}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6968607617792000960}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3941296933878034075
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1075647434011734032}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &8531807458618777853
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1075647434011734032}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1159571266054299744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7903946776649287414}
  - component: {fileID: 5008667842732756397}
  - component: {fileID: 4823856952303666953}
  - component: {fileID: 9131903630027520600}
  m_Layer: 0
  m_Name: Wood_Floor_Piece4 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7903946776649287414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159571266054299744}
  m_LocalRotation: {x: 0.000000018812546, y: -0.99998707, z: 0.00000003278989, w: 0.005090333}
  m_LocalPosition: {x: 0.12609957, y: 4.150499, z: -5.5538}
  m_LocalScale: {x: 0.9457439, y: 0.9457438, z: 0.9457439}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 68
  m_LocalEulerAnglesHint: {x: 0, y: -179.417, z: 0}
--- !u!33 &5008667842732756397
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159571266054299744}
  m_Mesh: {fileID: 2545581547402230364, guid: 79508dfc3e67c0b47858f8a36b60d149, type: 3}
--- !u!23 &4823856952303666953
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159571266054299744}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &9131903630027520600
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159571266054299744}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.682927, y: 0.097441405, z: 1.4208018}
  m_Center: {x: 0.20213024, y: -0.009633716, z: 0.020910298}
--- !u!1 &1167916491854940680
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2973137246160570312}
  - component: {fileID: 847784546696048951}
  - component: {fileID: 1421139109946539973}
  - component: {fileID: 6508926708592706707}
  m_Layer: 0
  m_Name: Wood_Fence2 (17)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2973137246160570312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1167916491854940680}
  m_LocalRotation: {x: 0.000000019042673, y: 0.4907664, z: -0.0000000107260405, w: -0.87129116}
  m_LocalPosition: {x: -3.9682062, y: 4.161, z: -1.6389769}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 63
  m_LocalEulerAnglesHint: {x: 0, y: 301.218, z: 0}
--- !u!33 &847784546696048951
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1167916491854940680}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &1421139109946539973
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1167916491854940680}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6508926708592706707
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1167916491854940680}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996441, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011019707, y: 0.42235386, z: -0.0026088133}
--- !u!1 &1192270140626687332
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1957018464666469430}
  - component: {fileID: 850414206554690527}
  - component: {fileID: 5447789151270727919}
  - component: {fileID: 59991984991651918}
  m_Layer: 0
  m_Name: Wood_Floor_Piece5 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1957018464666469430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192270140626687332}
  m_LocalRotation: {x: -3.5527137e-15, y: -0.965753, z: 1.7763568e-15, w: 0.25946334}
  m_LocalPosition: {x: -0.0017525873, y: 10.026, z: -2.4109166}
  m_LocalScale: {x: 0.93928623, y: 0.93928593, z: 0.93928623}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 88
  m_LocalEulerAnglesHint: {x: 0, y: -149.924, z: 0}
--- !u!33 &850414206554690527
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192270140626687332}
  m_Mesh: {fileID: 551321949575145288, guid: 6235f8f6a81c9a945870b1e8ac3569f2, type: 3}
--- !u!23 &5447789151270727919
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192270140626687332}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &59991984991651918
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192270140626687332}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.6520398, y: 0.0974417, z: 6.0996885}
  m_Center: {x: 1.3370312, y: -0.008200743, z: 0.0011667816}
--- !u!1 &1198335940753142710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 172976387076194677}
  - component: {fileID: 1175343688801126503}
  - component: {fileID: 2694671355038266914}
  - component: {fileID: 1869078703936651325}
  m_Layer: 0
  m_Name: Wood_Stairs3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &172976387076194677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198335940753142710}
  m_LocalRotation: {x: -0, y: -0.24762064, z: -8.881784e-16, w: 0.9688571}
  m_LocalPosition: {x: -0.95100033, y: 1.3799998, z: -4.195}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 26
  m_LocalEulerAnglesHint: {x: 0, y: -28.674, z: 0}
--- !u!33 &1175343688801126503
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198335940753142710}
  m_Mesh: {fileID: 7656056892305370255, guid: c63846f1b1c30954e94093fb7b1c4e06, type: 3}
--- !u!23 &2694671355038266914
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198335940753142710}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1e9b5d5fe1b901848864f2b060eec56a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1869078703936651325
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198335940753142710}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.6729946, y: 3.1261024, z: 0.16418755}
  m_Center: {x: 0.0006673336, y: 1.5604019, z: -0.0235886}
--- !u!1 &1248429064086210134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 545922097178437189}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &545922097178437189
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1248429064086210134}
  m_LocalRotation: {x: 2.1265521e-10, y: 0.99995273, z: -0.00000002185466, w: -0.009729966}
  m_LocalPosition: {x: -0.3790004, y: 5.739, z: -5.2470007}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6335609705990960771}
  - {fileID: 1579540787178475890}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 34
  m_LocalEulerAnglesHint: {x: 0, y: 181.115, z: 0}
--- !u!1 &1321089706227214362
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 149702861605976870}
  - component: {fileID: 2787192742875151423}
  - component: {fileID: 6007265654198706702}
  - component: {fileID: 3639929574562525451}
  m_Layer: 0
  m_Name: Firewoods
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &149702861605976870
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1321089706227214362}
  m_LocalRotation: {x: 4.255143e-10, y: 0.99981046, z: -0.000000021851552, w: -0.01946926}
  m_LocalPosition: {x: 1.0739995, y: 1.4429997, z: -4.627}
  m_LocalScale: {x: 0.79999995, y: 0.8, z: 0.79999995}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 182.231, z: 0}
--- !u!33 &2787192742875151423
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1321089706227214362}
  m_Mesh: {fileID: -6438214064732730673, guid: ff0651c735c27b0488fcc30a7ffa6a21, type: 3}
--- !u!23 &6007265654198706702
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1321089706227214362}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 48cb4cc03005d5d46a2e1a2ba4329822, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &3639929574562525451
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1321089706227214362}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -6438214064732730673, guid: ff0651c735c27b0488fcc30a7ffa6a21, type: 3}
--- !u!1 &1348772437431206892
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 92280352549398529}
  - component: {fileID: 5549689904649088127}
  - component: {fileID: 3104540038493625268}
  - component: {fileID: 8068357829878029225}
  m_Layer: 0
  m_Name: Wood_Fence2 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &92280352549398529
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348772437431206892}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 1.4870001, y: 4.161, z: 1.3559996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 49
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5549689904649088127
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348772437431206892}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &3104540038493625268
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348772437431206892}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8068357829878029225
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348772437431206892}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996441, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022091, y: 0.42235386, z: -0.002608575}
--- !u!1 &1376949374553104723
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 908477908410909587}
  - component: {fileID: 7316586025779919970}
  - component: {fileID: 7712204989612397755}
  - component: {fileID: 8438289212689337296}
  m_Layer: 0
  m_Name: Wood_Floor_Piece4 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &908477908410909587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376949374553104723}
  m_LocalRotation: {x: -5.3290705e-15, y: -0.86483735, z: 0.000000037803275, w: 0.5020522}
  m_LocalPosition: {x: -2.6969004, y: 4.1504993, z: -4.1098}
  m_LocalScale: {x: 0.9457437, y: 0.9457438, z: 0.94575363}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 67
  m_LocalEulerAnglesHint: {x: 0, y: -119.728, z: 0}
--- !u!33 &7316586025779919970
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376949374553104723}
  m_Mesh: {fileID: 2545581547402230364, guid: 79508dfc3e67c0b47858f8a36b60d149, type: 3}
--- !u!23 &7712204989612397755
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376949374553104723}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8438289212689337296
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376949374553104723}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.682927, y: 0.097441405, z: 1.4208015}
  m_Center: {x: 0.20213024, y: -0.009633716, z: 0.020909818}
--- !u!1 &1422261176280266596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8377976602938810851}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8377976602938810851
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1422261176280266596}
  m_LocalRotation: {x: -0.000000018768995, y: 0.5123629, z: -0.000000011198045, w: 0.8587691}
  m_LocalPosition: {x: 2.5489454, y: 11.65, z: -1.2621725}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6748324782644511564}
  - {fileID: 3659115740717765413}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 38
  m_LocalEulerAnglesHint: {x: 0, y: 61.643, z: 0}
--- !u!1 &1495996521602589358
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2281548719013139875}
  - component: {fileID: 4936253099925410022}
  - component: {fileID: 3171032361908856463}
  - component: {fileID: 5930680767636101722}
  m_Layer: 0
  m_Name: Lantern
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2281548719013139875
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1495996521602589358}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 1.2499996, y: 2.2689998, z: -3.955}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4936253099925410022
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1495996521602589358}
  m_Mesh: {fileID: -5420149580168298667, guid: baa32f00720af2349a953fda8b55943d, type: 3}
--- !u!23 &3171032361908856463
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1495996521602589358}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: df3eb4d9c0f5146448b7660b8ecbca95, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5930680767636101722
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1495996521602589358}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.28843904, y: 0.39947283, z: 0.26162088}
  m_Center: {x: 0.016164035, y: 0.1996105, z: 0.00068069994}
--- !u!1 &1603965760093399205
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8059877316471258679}
  - component: {fileID: 6362120942420837478}
  - component: {fileID: 4290299751525677924}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8059877316471258679
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603965760093399205}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4640775638103698599}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6362120942420837478
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603965760093399205}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &4290299751525677924
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603965760093399205}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1716762036758552792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1184098731980822511}
  - component: {fileID: 7216859772892400518}
  - component: {fileID: 8330058738221329338}
  - component: {fileID: 1469310148973007095}
  m_Layer: 0
  m_Name: Wood_Floor_Piece6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1184098731980822511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716762036758552792}
  m_LocalRotation: {x: 1.7763568e-15, y: -0.2588186, z: -1.3322676e-15, w: 0.965926}
  m_LocalPosition: {x: -0.05500028, y: 4.2527, z: -2.4139998}
  m_LocalScale: {x: 0.930462, y: 0.93046194, z: 0.930462}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: -30, z: 0}
--- !u!33 &7216859772892400518
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716762036758552792}
  m_Mesh: {fileID: 2860320775646373785, guid: b361263c1c27d844381325cdaef513b8, type: 3}
--- !u!23 &8330058738221329338
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716762036758552792}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &1469310148973007095
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716762036758552792}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 2860320775646373785, guid: b361263c1c27d844381325cdaef513b8, type: 3}
--- !u!1 &2033875342349688983
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7809958524878278463}
  - component: {fileID: 1385497715717055868}
  - component: {fileID: 1633609212507362737}
  - component: {fileID: 5381214026163861600}
  m_Layer: 0
  m_Name: Wood_Floor_Piece5 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7809958524878278463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2033875342349688983}
  m_LocalRotation: {x: -7.1054274e-15, y: -0.706635, z: 5.3290705e-15, w: 0.7075783}
  m_LocalPosition: {x: -0.027115796, y: 7.1243, z: -2.4256713}
  m_LocalScale: {x: 0.93928605, y: 0.93928593, z: 0.93928605}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 79
  m_LocalEulerAnglesHint: {x: 0, y: -89.924, z: 0}
--- !u!33 &1385497715717055868
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2033875342349688983}
  m_Mesh: {fileID: 551321949575145288, guid: 6235f8f6a81c9a945870b1e8ac3569f2, type: 3}
--- !u!23 &1633609212507362737
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2033875342349688983}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5381214026163861600
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2033875342349688983}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.6520383, y: 0.0974417, z: 6.0996866}
  m_Center: {x: 1.3370308, y: -0.008200743, z: 0.0011667014}
--- !u!1 &2103846355102612051
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5233481901258132563}
  - component: {fileID: 1276823434959955680}
  - component: {fileID: 8201030399421627404}
  - component: {fileID: 5478137757906553632}
  m_Layer: 0
  m_Name: Wood_Floor_Piece4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5233481901258132563
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2103846355102612051}
  m_LocalRotation: {x: 5.8619776e-14, y: 0.0015192698, z: -6.6406575e-11, w: 0.99999887}
  m_LocalPosition: {x: -0.20892994, y: 4.1504993, z: 0.7701298}
  m_LocalScale: {x: 0.9457438, y: 0.9457438, z: 0.9457438}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 47
  m_LocalEulerAnglesHint: {x: 0, y: 0.174, z: 0}
--- !u!33 &1276823434959955680
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2103846355102612051}
  m_Mesh: {fileID: 2545581547402230364, guid: 79508dfc3e67c0b47858f8a36b60d149, type: 3}
--- !u!23 &8201030399421627404
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2103846355102612051}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5478137757906553632
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2103846355102612051}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.682926, y: 0.097441405, z: 1.4208014}
  m_Center: {x: 0.2021302, y: -0.009633716, z: 0.020909816}
--- !u!1 &2192104761784390240
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5991552153904099626}
  - component: {fileID: 4276646609702976695}
  - component: {fileID: 3483995714662822884}
  - component: {fileID: 8155723522267387186}
  m_Layer: 0
  m_Name: Wood_Fence2 (22)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5991552153904099626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192104761784390240}
  m_LocalRotation: {x: -1.134044e-10, y: -0.5044869, z: -0.000000021855396, w: 0.86341935}
  m_LocalPosition: {x: 1.0006063, y: 10.05, z: -2.6670523}
  m_LocalScale: {x: 1.1008002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 85
  m_LocalEulerAnglesHint: {x: 0, y: -60.595, z: 0}
--- !u!33 &4276646609702976695
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192104761784390240}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &3483995714662822884
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192104761784390240}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8155723522267387186
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192104761784390240}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011023284, y: 0.42235386, z: -0.002608575}
--- !u!1 &2201489720682474180
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5177085490542485530}
  - component: {fileID: 8610064409037796887}
  - component: {fileID: 4875731342095906381}
  - component: {fileID: 3313839000439032955}
  m_Layer: 0
  m_Name: Stone_Base (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5177085490542485530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2201489720682474180}
  m_LocalRotation: {x: -0.000000018913946, y: 0.5010795, z: -0.000000010951439, w: 0.86540127}
  m_LocalPosition: {x: 2.3706331, y: 0, z: -1.2385972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 22
  m_LocalEulerAnglesHint: {x: 0, y: 60.143, z: 0}
--- !u!33 &8610064409037796887
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2201489720682474180}
  m_Mesh: {fileID: 6755529176504839361, guid: 9b173924ab9d78f4bb9c2a0621b8adbd, type: 3}
--- !u!23 &4875731342095906381
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2201489720682474180}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb1d374a86584b24ab631bd9accb85b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3313839000439032955
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2201489720682474180}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.7580094, y: 1.3246915, z: 1.3280057}
  m_Center: {x: -0.018235626, y: 0.6601888, z: -0.010336966}
--- !u!1 &2350766182480031797
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1040754439872378882}
  - component: {fileID: 4866189976286631644}
  - component: {fileID: 5789193214134024814}
  - component: {fileID: 8651157907823635098}
  m_Layer: 0
  m_Name: Wall1_Door1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1040754439872378882
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350766182480031797}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: 0.5022697, y: -1.6112084, z: 0.52654314}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7743848445515895267}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4866189976286631644
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350766182480031797}
  m_Mesh: {fileID: -7963949379387693633, guid: a2edd7d5877ff234884ee897708745f6, type: 3}
--- !u!23 &5789193214134024814
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350766182480031797}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ce0d49ee954d64241908acdf028a06ab, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &8651157907823635098
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350766182480031797}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: -7963949379387693633, guid: a2edd7d5877ff234884ee897708745f6, type: 3}
--- !u!1 &2358854923914494977
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4379947272523327790}
  - component: {fileID: 7169615366579809682}
  - component: {fileID: 8398264303439351685}
  - component: {fileID: 8440179309398186886}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4379947272523327790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2358854923914494977}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7578924180870275081}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7169615366579809682
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2358854923914494977}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &8398264303439351685
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2358854923914494977}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8440179309398186886
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2358854923914494977}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007925, y: 3.011355, z: 0.31099507}
  m_Center: {x: 0.1196744, y: 1.4968741, z: 0.0008882062}
--- !u!1 &2378803104074468589
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4336368278658314557}
  - component: {fileID: 5237235870536370266}
  - component: {fileID: 7404440313337504832}
  - component: {fileID: 7011650711837640366}
  m_Layer: 0
  m_Name: Stone_Base (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4336368278658314557
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378803104074468589}
  m_LocalRotation: {x: -6.346711e-14, y: 1, z: -0.000000021855694, w: 0.000002906718}
  m_LocalPosition: {x: -0.35000035, y: 0, z: -4.98}
  m_LocalScale: {x: 1, y: 0.99005, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 20
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!33 &5237235870536370266
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378803104074468589}
  m_Mesh: {fileID: 6755529176504839361, guid: 9b173924ab9d78f4bb9c2a0621b8adbd, type: 3}
--- !u!23 &7404440313337504832
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378803104074468589}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb1d374a86584b24ab631bd9accb85b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7011650711837640366
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378803104074468589}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.758009, y: 1.3246914, z: 1.3280056}
  m_Center: {x: -0.018235624, y: 0.66018873, z: -0.010336965}
--- !u!1 &2483635446372624462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8016360245613840858}
  - component: {fileID: 2351616693688654638}
  - component: {fileID: 8842762818792224251}
  - component: {fileID: 1056268251613092929}
  m_Layer: 0
  m_Name: Wood_Fence2 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8016360245613840858
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2483635446372624462}
  m_LocalRotation: {x: -0.000000018879541, y: 0.5037885, z: -0.000000011010646, w: 0.8638271}
  m_LocalPosition: {x: 3.2639818, y: 4.161, z: -0.5819975}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 51
  m_LocalEulerAnglesHint: {x: 0, y: 60.502, z: 0}
--- !u!33 &2351616693688654638
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2483635446372624462}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &8842762818792224251
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2483635446372624462}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1056268251613092929
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2483635446372624462}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607018}
  m_Center: {x: -0.0011022092, y: 0.42235386, z: -0.0026085752}
--- !u!1 &2654715370607477273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8972414689347049670}
  - component: {fileID: 6864535485802594204}
  - component: {fileID: 6112763459046020750}
  - component: {fileID: 2010610905225871219}
  m_Layer: 0
  m_Name: Roof3_9
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8972414689347049670
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2654715370607477273}
  m_LocalRotation: {x: 3.5527137e-15, y: -0.004812679, z: 2.1036883e-10, w: 0.99998844}
  m_LocalPosition: {x: -0.029806593, y: 13.401999, z: -2.4897451}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: -0.551, z: 0}
--- !u!33 &6864535485802594204
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2654715370607477273}
  m_Mesh: {fileID: -416846977410943435, guid: 09820d2dd07079646a74d85617808d1e, type: 3}
--- !u!23 &6112763459046020750
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2654715370607477273}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 503e6cf09b068cb46aa0bf06219e8378, type: 2}
  - {fileID: 2100000, guid: 503e6cf09b068cb46aa0bf06219e8378, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &2010610905225871219
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2654715370607477273}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -416846977410943435, guid: 09820d2dd07079646a74d85617808d1e, type: 3}
--- !u!1 &2660521097572251636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5649310263197189854}
  - component: {fileID: 283890993896576135}
  - component: {fileID: 3113755232096683244}
  - component: {fileID: 2720894088322476776}
  m_Layer: 0
  m_Name: Wood_Fence2 (16)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5649310263197189854
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2660521097572251636}
  m_LocalRotation: {x: 0.000000019042673, y: 0.4907664, z: -0.0000000107260405, w: -0.87129116}
  m_LocalPosition: {x: -3.237926, y: 4.161, z: -0.43399993}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 64
  m_LocalEulerAnglesHint: {x: 0, y: 301.218, z: 0}
--- !u!33 &283890993896576135
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2660521097572251636}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &3113755232096683244
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2660521097572251636}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2720894088322476776
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2660521097572251636}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996441, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022091, y: 0.42235386, z: -0.0026088133}
--- !u!1 &2683300976106095542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7743848445515895267}
  m_Layer: 0
  m_Name: Wall1_SW1_Door1_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7743848445515895267
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2683300976106095542}
  m_LocalRotation: {x: -0.000000021855628, y: 0.0024565274, z: -5.3689168e-11, w: 0.999997}
  m_LocalPosition: {x: -0.4815617, y: 2.85, z: -0.39825413}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1040754439872378882}
  - {fileID: 9098947471239458088}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0.281, z: 0}
--- !u!1 &2716321149682838345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6220439204156561835}
  - component: {fileID: 95216645719988168}
  - component: {fileID: 2685643068472172582}
  - component: {fileID: 367581968407440836}
  m_Layer: 0
  m_Name: Wood_Floor_Piece5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6220439204156561835
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2716321149682838345}
  m_LocalRotation: {x: -1.7763568e-15, y: -0.25817484, z: -8.881784e-16, w: 0.96609825}
  m_LocalPosition: {x: -0.054028247, y: 1.3684999, z: -2.4246}
  m_LocalScale: {x: 0.93928593, y: 0.93928593, z: 0.93928593}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 56
  m_LocalEulerAnglesHint: {x: 0, y: -29.924, z: 0}
--- !u!33 &95216645719988168
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2716321149682838345}
  m_Mesh: {fileID: 551321949575145288, guid: 6235f8f6a81c9a945870b1e8ac3569f2, type: 3}
--- !u!23 &2685643068472172582
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2716321149682838345}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &367581968407440836
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2716321149682838345}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.6520383, y: 0.0974417, z: 6.0996866}
  m_Center: {x: 1.3370308, y: -0.008200743, z: 0.0011667014}
--- !u!1 &2824681370933235945
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1868244983850905842}
  - component: {fileID: 3070720564450762103}
  - component: {fileID: 8339067310246018604}
  - component: {fileID: 6187486826974766021}
  m_Layer: 0
  m_Name: Wood_Fence2 (12)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1868244983850905842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2824681370933235945}
  m_LocalRotation: {x: 0.000000010945983, y: 0.86554587, z: -0.000000018917106, w: -0.5008298}
  m_LocalPosition: {x: -2.6360004, y: 4.161, z: -5.397}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 62
  m_LocalEulerAnglesHint: {x: 0, y: 240.11, z: 0}
--- !u!33 &3070720564450762103
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2824681370933235945}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &8339067310246018604
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2824681370933235945}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6187486826974766021
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2824681370933235945}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996449, y: 0.8446262, z: 0.16607024}
  m_Center: {x: -0.0011019713, y: 0.42235386, z: -0.002608576}
--- !u!1 &2835328214924253532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5081446262162994880}
  - component: {fileID: 441175608298872390}
  - component: {fileID: 4160787599417866119}
  - component: {fileID: 2386763875737694617}
  m_Layer: 0
  m_Name: Wall3_CW1 (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5081446262162994880
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2835328214924253532}
  m_LocalRotation: {x: -0.000000021855694, y: -0.00007794012, z: 1.7033744e-12, w: 1}
  m_LocalPosition: {x: -0.043999422, y: 7.155, z: 0.120999746}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 46
  m_LocalEulerAnglesHint: {x: 0, y: -0.009, z: 0}
--- !u!33 &441175608298872390
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2835328214924253532}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &4160787599417866119
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2835328214924253532}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2386763875737694617
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2835328214924253532}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079246, y: 3.0155451, z: 0.31099504}
  m_Center: {x: 0.114536464, y: 1.5006055, z: 0.00088807195}
--- !u!1 &2913343792032065772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8706252033252458291}
  - component: {fileID: 7892845158111555535}
  - component: {fileID: 201239530643878980}
  m_Layer: 0
  m_Name: Wood_Suport1 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8706252033252458291
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2913343792032065772}
  m_LocalRotation: {x: -0.000000005261648, y: -0.97058845, z: 0.000000021212884, w: 0.24074495}
  m_LocalPosition: {x: -1.8560004, y: 3.8968997, z: -5.426}
  m_LocalScale: {x: 0.8851, y: 0.8851, z: 0.8851}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 75
  m_LocalEulerAnglesHint: {x: 0, y: -152.139, z: 0}
--- !u!33 &7892845158111555535
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2913343792032065772}
  m_Mesh: {fileID: 2441783984478029798, guid: 4590fc541dac1874a9ee25a7a0b25a88, type: 3}
--- !u!23 &201239530643878980
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2913343792032065772}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37cd02b95341e4149b25001484bfd014, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2998070670207852328
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7831238503454576133}
  - component: {fileID: 3379393584755078820}
  - component: {fileID: 7067192304478956052}
  - component: {fileID: 662139350159219533}
  m_Layer: 0
  m_Name: Box1 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7831238503454576133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2998070670207852328}
  m_LocalRotation: {x: 0.000000010372777, y: -0.8802, z: 0.000000019237383, w: -0.474603}
  m_LocalPosition: {x: -2.612, y: 1.188, z: 0.84400016}
  m_LocalScale: {x: 0.8000001, y: 0.8, z: 0.8000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 94
  m_LocalEulerAnglesHint: {x: 0, y: -236.66699, z: 0}
--- !u!33 &3379393584755078820
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2998070670207852328}
  m_Mesh: {fileID: 1850462315688071848, guid: 3cedb26e22742574ea0002576e656359, type: 3}
--- !u!23 &7067192304478956052
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2998070670207852328}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b42e40c611a2d12488635e75af670899, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &662139350159219533
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2998070670207852328}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0368521, y: 0.98749745, z: 1.048191}
  m_Center: {x: -0.00006809812, y: -0.001739502, z: 0.0067925467}
--- !u!1 &3041164702400536225
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 715866897245983922}
  - component: {fileID: 9037295223144274501}
  - component: {fileID: 4822509870828083334}
  - component: {fileID: 5595294405111944493}
  m_Layer: 0
  m_Name: Wall3_CW1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &715866897245983922
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3041164702400536225}
  m_LocalRotation: {x: -0.0000000108108695, y: 0.86909354, z: -0.000000018994642,
    w: 0.49464774}
  m_LocalPosition: {x: 2.0819995, y: 4.207, z: -3.7000008}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 31
  m_LocalEulerAnglesHint: {x: 0, y: 120.707, z: 0}
--- !u!33 &9037295223144274501
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3041164702400536225}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &4822509870828083334
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3041164702400536225}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5595294405111944493
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3041164702400536225}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079243, y: 3.0155451, z: 0.310995}
  m_Center: {x: 0.114536695, y: 1.5006055, z: 0.0008880719}
--- !u!1 &3042254786063970446
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5644942449178350806}
  - component: {fileID: 4361798150440712209}
  - component: {fileID: 2467683785335153633}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5644942449178350806
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3042254786063970446}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4515221368256528461}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4361798150440712209
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3042254786063970446}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &2467683785335153633
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3042254786063970446}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3171504090330560275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8705004371730268911}
  - component: {fileID: 6237818368209730159}
  - component: {fileID: 2437371913282360824}
  - component: {fileID: 4702016152771146366}
  m_Layer: 0
  m_Name: Wood_Floor_Piece6 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8705004371730268911
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3171504090330560275}
  m_LocalRotation: {x: -0, y: -0.7071064, z: 7.1054274e-15, w: 0.7071072}
  m_LocalPosition: {x: -0.022883866, y: 7.141, z: -2.4223413}
  m_LocalScale: {x: 0.93046194, y: 0.93046194, z: 0.93046194}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 80
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &6237818368209730159
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3171504090330560275}
  m_Mesh: {fileID: 2860320775646373785, guid: b361263c1c27d844381325cdaef513b8, type: 3}
--- !u!23 &2437371913282360824
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3171504090330560275}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &4702016152771146366
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3171504090330560275}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 2860320775646373785, guid: b361263c1c27d844381325cdaef513b8, type: 3}
--- !u!1 &3240682398358435737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1343686082861796594}
  - component: {fileID: 8050932200485041535}
  - component: {fileID: 9181152399315859701}
  - component: {fileID: 3222728588004893319}
  m_Layer: 0
  m_Name: Wood_Fence2 (13)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1343686082861796594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3240682398358435737}
  m_LocalRotation: {x: 0.000000010945983, y: 0.86554587, z: -0.000000018917106, w: -0.5008298}
  m_LocalPosition: {x: -3.3381581, y: 4.161, z: -4.175421}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 61
  m_LocalEulerAnglesHint: {x: 0, y: 240.11, z: 0}
--- !u!33 &8050932200485041535
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3240682398358435737}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &9181152399315859701
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3240682398358435737}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3222728588004893319
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3240682398358435737}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996449, y: 0.8446262, z: 0.16607024}
  m_Center: {x: -0.0011023289, y: 0.42235386, z: -0.002608576}
--- !u!1 &3261625671134112752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9067234185665267967}
  - component: {fileID: 3085912971988161086}
  - component: {fileID: 5788969947926080314}
  - component: {fileID: 3090126501908916396}
  m_Layer: 0
  m_Name: Stone_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9067234185665267967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3261625671134112752}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.08000004}
  m_LocalScale: {x: 1, y: 0.9671, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 19
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3085912971988161086
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3261625671134112752}
  m_Mesh: {fileID: 6755529176504839361, guid: 9b173924ab9d78f4bb9c2a0621b8adbd, type: 3}
--- !u!23 &5788969947926080314
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3261625671134112752}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb1d374a86584b24ab631bd9accb85b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3090126501908916396
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3261625671134112752}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.758009, y: 1.3246915, z: 1.3280056}
  m_Center: {x: -0.018235624, y: 0.6601888, z: -0.010336965}
--- !u!1 &3268878371831244050
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3779552658310329500}
  - component: {fileID: 7998158772631445055}
  - component: {fileID: 4205636379950592834}
  - component: {fileID: 8315851337101097972}
  m_Layer: 0
  m_Name: Wood_Fence2 (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3779552658310329500
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3268878371831244050}
  m_LocalRotation: {x: 2.4641314e-10, y: 0.9999365, z: -0.000000021854307, w: -0.011274546}
  m_LocalPosition: {x: -0.13235599, y: 4.1609993, z: -6.1326604}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 58
  m_LocalEulerAnglesHint: {x: 0, y: 181.292, z: 0}
--- !u!33 &7998158772631445055
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3268878371831244050}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &4205636379950592834
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3268878371831244050}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8315851337101097972
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3268878371831244050}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.399645, y: 0.8446262, z: 0.16607027}
  m_Center: {x: -0.0011022098, y: 0.42235386, z: -0.0026090534}
--- !u!1 &3322876624596657953
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6968607617792000960}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6968607617792000960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3322876624596657953}
  m_LocalRotation: {x: 2.1265521e-10, y: 0.99995273, z: -0.00000002185466, w: -0.009729966}
  m_LocalPosition: {x: -0.3790004, y: 11.65, z: -5.2470007}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 244674236906086019}
  - {fileID: 4397528442804326718}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 39
  m_LocalEulerAnglesHint: {x: 0, y: 181.115, z: 0}
--- !u!1 &3513263722023756876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6004042591845969263}
  - component: {fileID: 1424028421068365885}
  - component: {fileID: 2153989900308882592}
  - component: {fileID: 903595470491080960}
  m_Layer: 0
  m_Name: Wall1_SW1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6004042591845969263
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3513263722023756876}
  m_LocalRotation: {x: -0.000000018645382, y: 0.5217259, z: -0.00000001140268, w: 0.8531131}
  m_LocalPosition: {x: 2.178, y: 1.2400002, z: -1.1480002}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 62.896, z: 0}
--- !u!33 &1424028421068365885
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3513263722023756876}
  m_Mesh: {fileID: 6375708778553226300, guid: 6f9a1c867ec13d8429a746ad701bd914, type: 3}
--- !u!23 &2153989900308882592
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3513263722023756876}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ce0d49ee954d64241908acdf028a06ab, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &903595470491080960
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3513263722023756876}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.99254, y: 3.011355, z: 0.310995}
  m_Center: {x: 0.12261389, y: 1.5053194, z: 0.00088810164}
--- !u!1 &3596751722876221335
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8752635699944493626}
  - component: {fileID: 5712554724897253169}
  - component: {fileID: 4906281562610359916}
  - component: {fileID: 7694630014645622991}
  m_Layer: 0
  m_Name: Bottle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8752635699944493626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3596751722876221335}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.9571, y: 1.292, z: 0.17830013}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5712554724897253169
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3596751722876221335}
  m_Mesh: {fileID: -8809766287632686279, guid: a3c3794f1521db64dbbde264d8ff66bc, type: 3}
--- !u!23 &4906281562610359916
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3596751722876221335}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 91b13b26fdd87d24e9426ab38e44b5c9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &7694630014645622991
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3596751722876221335}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.062437944
  m_Height: 0.26167887
  m_Direction: 1
  m_Center: {x: -0.00011897087, y: 0.13076371, z: 0.0007873159}
--- !u!1 &3733041560967279396
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9003453355065971549}
  - component: {fileID: 316215202513522678}
  - component: {fileID: 5437736926056927652}
  - component: {fileID: 3018543321125910658}
  m_Layer: 0
  m_Name: Wood_Fence2 (19)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9003453355065971549
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3733041560967279396}
  m_LocalRotation: {x: -0.000000018984034, y: 0.49549967, z: -0.000000010829489, w: 0.8686082}
  m_LocalPosition: {x: -1.5723201, y: 4.26, z: -1.8166}
  m_LocalScale: {x: 1.1008003, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 78
  m_LocalEulerAnglesHint: {x: 0, y: 59.405, z: 0}
--- !u!33 &316215202513522678
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3733041560967279396}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &5437736926056927652
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3733041560967279396}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3018543321125910658
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3733041560967279396}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022091, y: 0.42235386, z: -0.002608575}
--- !u!1 &3765188740643727721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8669051788172657843}
  - component: {fileID: 330803612534027320}
  - component: {fileID: 758662556214659308}
  - component: {fileID: 7825923863280857689}
  m_Layer: 0
  m_Name: Box1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8669051788172657843
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3765188740643727721}
  m_LocalRotation: {x: -0.000000021090045, y: -0.26236746, z: 0.000000005734223, w: 0.964968}
  m_LocalPosition: {x: 2.0939996, y: 1.784, z: -2.3050003}
  m_LocalScale: {x: 0.80000013, y: 0.8, z: 0.80000013}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: -30.421, z: 0}
--- !u!33 &330803612534027320
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3765188740643727721}
  m_Mesh: {fileID: 1850462315688071848, guid: 3cedb26e22742574ea0002576e656359, type: 3}
--- !u!23 &758662556214659308
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3765188740643727721}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b42e40c611a2d12488635e75af670899, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7825923863280857689
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3765188740643727721}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0368518, y: 0.98749745, z: 1.0481906}
  m_Center: {x: -0.000068157904, y: -0.001739502, z: 0.0067927833}
--- !u!1 &3906784655860851382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7095492545563855499}
  - component: {fileID: 7460055407324771146}
  - component: {fileID: 1012194050416101944}
  - component: {fileID: 8193862337608526050}
  m_Layer: 0
  m_Name: Stone_Base (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7095492545563855499
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3906784655860851382}
  m_LocalRotation: {x: -0.000000010583827, y: 0.8749245, z: -0.000000019122082, w: 0.4842595}
  m_LocalPosition: {x: 2.26, y: 0, z: -3.6600006}
  m_LocalScale: {x: 1, y: 1.0299, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 21
  m_LocalEulerAnglesHint: {x: 0, y: 122.072, z: 0}
--- !u!33 &7460055407324771146
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3906784655860851382}
  m_Mesh: {fileID: 6755529176504839361, guid: 9b173924ab9d78f4bb9c2a0621b8adbd, type: 3}
--- !u!23 &1012194050416101944
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3906784655860851382}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb1d374a86584b24ab631bd9accb85b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8193862337608526050
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3906784655860851382}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.75801, y: 1.3246915, z: 1.3280059}
  m_Center: {x: -0.018235628, y: 0.6601888, z: -0.010336968}
--- !u!1 &3927145639723175982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1714507936201856072}
  - component: {fileID: 7161235698390683727}
  - component: {fileID: 5608833075723487761}
  - component: {fileID: 2724052020569695551}
  m_Layer: 0
  m_Name: Stone_Base (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1714507936201856072
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3927145639723175982}
  m_LocalRotation: {x: -0.000000019129951, y: -0.48360828, z: 0.000000010569593, w: 0.87528455}
  m_LocalPosition: {x: -2.616, y: 0, z: -1.2097743}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 23
  m_LocalEulerAnglesHint: {x: 0, y: -57.843, z: 0}
--- !u!33 &7161235698390683727
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3927145639723175982}
  m_Mesh: {fileID: 6755529176504839361, guid: 9b173924ab9d78f4bb9c2a0621b8adbd, type: 3}
--- !u!23 &5608833075723487761
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3927145639723175982}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb1d374a86584b24ab631bd9accb85b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2724052020569695551
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3927145639723175982}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.758009, y: 1.3246915, z: 1.3280056}
  m_Center: {x: -0.018235624, y: 0.6601888, z: -0.010336965}
--- !u!1 &4176776940317680521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1177222000513442703}
  - component: {fileID: 1457804673981243717}
  - component: {fileID: 3626445868266309651}
  - component: {fileID: 1365315706780291804}
  m_Layer: 0
  m_Name: Wood_Floor_Piece5 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1177222000513442703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176776940317680521}
  m_LocalRotation: {x: 1.3322676e-15, y: 0.9660984, z: -0, w: 0.25817433}
  m_LocalPosition: {x: -0.039000247, y: 1.3684999, z: -2.416}
  m_LocalScale: {x: 0.9392866, y: 0.93928593, z: 0.9392866}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 71
  m_LocalEulerAnglesHint: {x: 0, y: 150.076, z: 0}
--- !u!33 &1457804673981243717
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176776940317680521}
  m_Mesh: {fileID: 551321949575145288, guid: 6235f8f6a81c9a945870b1e8ac3569f2, type: 3}
--- !u!23 &3626445868266309651
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176776940317680521}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1365315706780291804
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4176776940317680521}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.6520398, y: 0.0974417, z: 6.0996885}
  m_Center: {x: 1.3370312, y: -0.008200743, z: 0.0011667816}
--- !u!1 &4255154572405326533
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5455893183037013802}
  - component: {fileID: 5980136953008900399}
  - component: {fileID: 1928276654882696507}
  - component: {fileID: 4318950622732266084}
  m_Layer: 0
  m_Name: Wood_Stairs3 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5455893183037013802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4255154572405326533}
  m_LocalRotation: {x: -1.7763568e-15, y: -0.69887424, z: 3.5527137e-15, w: 0.7152446}
  m_LocalPosition: {x: 1.071508, y: 4.2683, z: -4.088799}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 81
  m_LocalEulerAnglesHint: {x: 0, y: -88.674, z: 0}
--- !u!33 &5980136953008900399
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4255154572405326533}
  m_Mesh: {fileID: 7656056892305370255, guid: c63846f1b1c30954e94093fb7b1c4e06, type: 3}
--- !u!23 &1928276654882696507
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4255154572405326533}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1e9b5d5fe1b901848864f2b060eec56a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4318950622732266084
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4255154572405326533}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.6729946, y: 3.1261024, z: 0.16418755}
  m_Center: {x: 0.0006673336, y: 1.5604019, z: -0.0235886}
--- !u!1 &4283997006222894935
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1217140957527276137}
  - component: {fileID: 3111824971015298175}
  - component: {fileID: 4747210761358471150}
  - component: {fileID: 5620345074475849299}
  m_Layer: 0
  m_Name: Wall3_CW1 (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1217140957527276137
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4283997006222894935}
  m_LocalRotation: {x: -0.000000021855694, y: -0.00007794012, z: 1.7033744e-12, w: 1}
  m_LocalPosition: {x: -0.043999422, y: 10.117999, z: 0.12099962}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 40
  m_LocalEulerAnglesHint: {x: 0, y: -0.009, z: 0}
--- !u!33 &3111824971015298175
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4283997006222894935}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &4747210761358471150
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4283997006222894935}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5620345074475849299
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4283997006222894935}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079246, y: 3.0155451, z: 0.31099504}
  m_Center: {x: 0.114536464, y: 1.5006055, z: 0.00088807195}
--- !u!1 &4750081080936511990
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 639657993405447789}
  - component: {fileID: 5670039208760434487}
  - component: {fileID: 645130587935635839}
  - component: {fileID: 8478024381832197088}
  m_Layer: 0
  m_Name: Wood_Fence2 (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &639657993405447789
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4750081080936511990}
  m_LocalRotation: {x: -0.000000010863008, y: 0.8677315, z: -0.000000018964874, w: 0.49703333}
  m_LocalPosition: {x: 3.1529994, y: 4.161, z: -4.3580003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 54
  m_LocalEulerAnglesHint: {x: 0, y: 120.392, z: 0}
--- !u!33 &5670039208760434487
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4750081080936511990}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &645130587935635839
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4750081080936511990}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8478024381832197088
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4750081080936511990}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996449, y: 0.8446262, z: 0.16607024}
  m_Center: {x: -0.0011022097, y: 0.42235386, z: -0.002608576}
--- !u!1 &4767418709362232639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4706187375655906167}
  - component: {fileID: 6662194681194090439}
  - component: {fileID: 8719777545815833982}
  - component: {fileID: 4502096525928131408}
  m_Layer: 0
  m_Name: Box1 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4706187375655906167
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4767418709362232639}
  m_LocalRotation: {x: 0.0000000059976433, y: -0.96160996, z: 0.000000021016653, w: -0.2744202}
  m_LocalPosition: {x: -2.38, y: 0.41300005, z: 1.1800002}
  m_LocalScale: {x: 0.8000006, y: 0.8, z: 0.8000006}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 92
  m_LocalEulerAnglesHint: {x: 0, y: -211.855, z: 0}
--- !u!33 &6662194681194090439
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4767418709362232639}
  m_Mesh: {fileID: 1850462315688071848, guid: 3cedb26e22742574ea0002576e656359, type: 3}
--- !u!23 &8719777545815833982
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4767418709362232639}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b42e40c611a2d12488635e75af670899, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4502096525928131408
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4767418709362232639}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0368526, y: 0.98749745, z: 1.0481914}
  m_Center: {x: -0.00006815836, y: -0.001739502, z: 0.0067925495}
--- !u!1 &4800073728453414786
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 244674236906086019}
  - component: {fileID: 4072235526590414511}
  - component: {fileID: 7919278569789352159}
  - component: {fileID: 7227049385742809178}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &244674236906086019
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4800073728453414786}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6968607617792000960}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4072235526590414511
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4800073728453414786}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &7919278569789352159
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4800073728453414786}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7227049385742809178
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4800073728453414786}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079265, y: 3.011355, z: 0.31099522}
  m_Center: {x: 0.11967451, y: 1.4968741, z: 0.0008880874}
--- !u!1 &4802511682305490410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4175548612110169461}
  - component: {fileID: 1903927526179647861}
  - component: {fileID: 5288143593629933264}
  - component: {fileID: 824314955383003463}
  m_Layer: 0
  m_Name: Door1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4175548612110169461
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4802511682305490410}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: 0.9413588, y: -1.5357565, z: 0.76493615}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 830962498211775844}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1903927526179647861
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4802511682305490410}
  m_Mesh: {fileID: -1856344252225202068, guid: 1ca9336098e8d2d428a32b7777aaed2f, type: 3}
--- !u!23 &5288143593629933264
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4802511682305490410}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 611b6187a8b10454da80524157a82496, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &824314955383003463
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4802511682305490410}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.91549987, y: 1.9428138, z: 0.13109477}
  m_Center: {x: -0.44574472, y: 0.9708784, z: 0.024385257}
--- !u!1 &5105548689902882169
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8967513207111667024}
  - component: {fileID: 7971431737240832710}
  - component: {fileID: 6744504039952803102}
  - component: {fileID: 1449237743342762009}
  m_Layer: 0
  m_Name: Wood_Fence2 (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8967513207111667024
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5105548689902882169}
  m_LocalRotation: {x: 2.4641314e-10, y: 0.9999365, z: -0.000000021854307, w: -0.011274546}
  m_LocalPosition: {x: -1.5360005, y: 4.1609993, z: -6.101}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 59
  m_LocalEulerAnglesHint: {x: 0, y: 181.292, z: 0}
--- !u!33 &7971431737240832710
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5105548689902882169}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &6744504039952803102
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5105548689902882169}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1449237743342762009
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5105548689902882169}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.399645, y: 0.8446262, z: 0.16607027}
  m_Center: {x: -0.0011022098, y: 0.42235386, z: -0.0026085766}
--- !u!1 &5116464836369479590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 292248333504136684}
  - component: {fileID: 6185526201734923029}
  - component: {fileID: 1513804369570903012}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &292248333504136684
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5116464836369479590}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7578924180870275081}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6185526201734923029
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5116464836369479590}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &1513804369570903012
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5116464836369479590}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5149470466330154642
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6866072507765017643}
  - component: {fileID: 2895024137242980172}
  - component: {fileID: 5969471780002419985}
  - component: {fileID: 7150394033469071070}
  m_Layer: 0
  m_Name: Stone_Base (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6866072507765017643
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5149470466330154642}
  m_LocalRotation: {x: 0.000000010927775, y: 0.8660274, z: -0.000000018927631, w: -0.4999967}
  m_LocalPosition: {x: -2.6990001, y: 0, z: -3.6799996}
  m_LocalScale: {x: 1, y: 1.0299, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 24
  m_LocalEulerAnglesHint: {x: 0, y: 240, z: 0}
--- !u!33 &2895024137242980172
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5149470466330154642}
  m_Mesh: {fileID: 6755529176504839361, guid: 9b173924ab9d78f4bb9c2a0621b8adbd, type: 3}
--- !u!23 &5969471780002419985
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5149470466330154642}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cb1d374a86584b24ab631bd9accb85b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7150394033469071070
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5149470466330154642}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.7580109, y: 1.3246915, z: 1.3280061}
  m_Center: {x: -0.018235633, y: 0.6601888, z: -0.01033697}
--- !u!1 &5291179683188977566
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4966421480140681318}
  - component: {fileID: 6462540681365620982}
  - component: {fileID: 6716474478882982580}
  - component: {fileID: 4896474001641635301}
  m_Layer: 0
  m_Name: Wood_Fence2 (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4966421480140681318
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5291179683188977566}
  m_LocalRotation: {x: -0.000000010863008, y: 0.8677315, z: -0.000000018964874, w: 0.49703333}
  m_LocalPosition: {x: 3.865836, y: 4.161, z: -3.142621}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 55
  m_LocalEulerAnglesHint: {x: 0, y: 120.392, z: 0}
--- !u!33 &6462540681365620982
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5291179683188977566}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &6716474478882982580
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5291179683188977566}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4896474001641635301
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5291179683188977566}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996449, y: 0.8446262, z: 0.16607024}
  m_Center: {x: -0.0011020905, y: 0.42235386, z: -0.0026080993}
--- !u!1 &5401405097898696101
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9098947471239458088}
  - component: {fileID: 3067609890826876787}
  - component: {fileID: 974406605269264080}
  - component: {fileID: 6192841502861444701}
  m_Layer: 0
  m_Name: Door1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9098947471239458088
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5401405097898696101}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: 0.9172697, y: -1.4702084, z: 0.5135431}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7743848445515895267}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3067609890826876787
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5401405097898696101}
  m_Mesh: {fileID: -1856344252225202068, guid: 1ca9336098e8d2d428a32b7777aaed2f, type: 3}
--- !u!23 &974406605269264080
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5401405097898696101}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 611b6187a8b10454da80524157a82496, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6192841502861444701
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5401405097898696101}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.91549987, y: 1.9428138, z: 0.13109477}
  m_Center: {x: -0.44574475, y: 0.9708784, z: 0.024385316}
--- !u!1 &5570096211223772944
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5450473943786070567}
  - component: {fileID: 2784874120005673722}
  - component: {fileID: 1090726719427179765}
  - component: {fileID: 4726639548303674020}
  m_Layer: 0
  m_Name: Wall1_SW1 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5450473943786070567
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5570096211223772944}
  m_LocalRotation: {x: 2.2199609e-10, y: 0.9999485, z: -0.000000021854568, w: -0.010157354}
  m_LocalPosition: {x: -0.08100052, y: 1.2399998, z: -4.9430003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 30
  m_LocalEulerAnglesHint: {x: 0, y: 181.164, z: 0}
--- !u!33 &2784874120005673722
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5570096211223772944}
  m_Mesh: {fileID: 6375708778553226300, guid: 6f9a1c867ec13d8429a746ad701bd914, type: 3}
--- !u!23 &1090726719427179765
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5570096211223772944}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ce0d49ee954d64241908acdf028a06ab, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4726639548303674020
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5570096211223772944}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.9925423, y: 3.011355, z: 0.31099525}
  m_Center: {x: 0.122613996, y: 1.5053194, z: 0.0008881024}
--- !u!1 &5603011370699711724
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3043933258195670363}
  - component: {fileID: 9001067800569224332}
  - component: {fileID: 5732546049337894437}
  - component: {fileID: 4616193611552715404}
  m_Layer: 0
  m_Name: Wood_Stairs3 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3043933258195670363
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5603011370699711724}
  m_LocalRotation: {x: 1.7763568e-15, y: -0.9628652, z: -0, w: 0.26998284}
  m_LocalPosition: {x: 1.9878705, y: 7.138, z: -2.291044}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 86
  m_LocalEulerAnglesHint: {x: 0, y: -148.674, z: 0}
--- !u!33 &9001067800569224332
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5603011370699711724}
  m_Mesh: {fileID: 7656056892305370255, guid: c63846f1b1c30954e94093fb7b1c4e06, type: 3}
--- !u!23 &5732546049337894437
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5603011370699711724}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1e9b5d5fe1b901848864f2b060eec56a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4616193611552715404
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5603011370699711724}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.67299485, y: 3.1261024, z: 0.16418761}
  m_Center: {x: 0.00066733384, y: 1.5604019, z: -0.023588609}
--- !u!1 &5622492944758378005
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1035774684743862908}
  m_Layer: 0
  m_Name: Windmill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1035774684743862908
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5622492944758378005}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7610069153945999192}
  - {fileID: 8752635699944493626}
  - {fileID: 8847710687903073269}
  - {fileID: 2281548719013139875}
  - {fileID: 149702861605976870}
  - {fileID: 1306587948705011145}
  - {fileID: 8669051788172657843}
  - {fileID: 4852979484439943246}
  - {fileID: 3656371530000266318}
  - {fileID: 1184098731980822511}
  - {fileID: 3197412588685102085}
  - {fileID: 2031982481864873830}
  - {fileID: 3808405946260538358}
  - {fileID: 8972414689347049670}
  - {fileID: 830962498211775844}
  - {fileID: 6602437169953551205}
  - {fileID: 6004042591845969263}
  - {fileID: 4058092835216825312}
  - {fileID: 7743848445515895267}
  - {fileID: 9067234185665267967}
  - {fileID: 4336368278658314557}
  - {fileID: 7095492545563855499}
  - {fileID: 5177085490542485530}
  - {fileID: 1714507936201856072}
  - {fileID: 6866072507765017643}
  - {fileID: 4853823847471940021}
  - {fileID: 172976387076194677}
  - {fileID: 2241021225350040009}
  - {fileID: 7462148121626589291}
  - {fileID: 3951332432746091754}
  - {fileID: 5450473943786070567}
  - {fileID: 715866897245983922}
  - {fileID: 3775488179122636188}
  - {fileID: 1620596581651661233}
  - {fileID: 545922097178437189}
  - {fileID: 8092404939750605447}
  - {fileID: 910552085028483228}
  - {fileID: 4640775638103698599}
  - {fileID: 8377976602938810851}
  - {fileID: 6968607617792000960}
  - {fileID: 1217140957527276137}
  - {fileID: 4515221368256528461}
  - {fileID: 3160055184589488195}
  - {fileID: 7578924180870275081}
  - {fileID: 8298637073956641249}
  - {fileID: 6602890253830961865}
  - {fileID: 5081446262162994880}
  - {fileID: 5233481901258132563}
  - {fileID: 2237261565201535675}
  - {fileID: 92280352549398529}
  - {fileID: 7334811720957015312}
  - {fileID: 8016360245613840858}
  - {fileID: 6511638242502295910}
  - {fileID: 3928528397785229217}
  - {fileID: 639657993405447789}
  - {fileID: 4966421480140681318}
  - {fileID: 6220439204156561835}
  - {fileID: 8478826543359794770}
  - {fileID: 3779552658310329500}
  - {fileID: 8967513207111667024}
  - {fileID: 6936545065285266943}
  - {fileID: 1343686082861796594}
  - {fileID: 1868244983850905842}
  - {fileID: 2973137246160570312}
  - {fileID: 5649310263197189854}
  - {fileID: 911324649808678380}
  - {fileID: 6418666949583980486}
  - {fileID: 908477908410909587}
  - {fileID: 7903946776649287414}
  - {fileID: 4486933498449375600}
  - {fileID: 1907422909260600636}
  - {fileID: 1177222000513442703}
  - {fileID: 3375618895100984472}
  - {fileID: 1676794121581795614}
  - {fileID: 5615303026752057446}
  - {fileID: 8706252033252458291}
  - {fileID: 6596972807547847089}
  - {fileID: 4954324626672949676}
  - {fileID: 9003453355065971549}
  - {fileID: 7809958524878278463}
  - {fileID: 8705004371730268911}
  - {fileID: 5455893183037013802}
  - {fileID: 2958477326508290203}
  - {fileID: 5484278389149518175}
  - {fileID: 5760419385270443054}
  - {fileID: 5991552153904099626}
  - {fileID: 3043933258195670363}
  - {fileID: 7175193782749266275}
  - {fileID: 1957018464666469430}
  - {fileID: 7090919684701402568}
  - {fileID: 8632464480615673788}
  - {fileID: 4859266332465925369}
  - {fileID: 4706187375655906167}
  - {fileID: 4796419371448874848}
  - {fileID: 7831238503454576133}
  - {fileID: 4726993891573842423}
  - {fileID: 486669099989841890}
  - {fileID: 1033648784343807891}
  - {fileID: 763926993752729306}
  - {fileID: 3066349006893725824}
  - {fileID: 7342721495556459802}
  - {fileID: 2094869388397618322}
  - {fileID: 5027897566589134923}
  - {fileID: 4797088890716812636}
  - {fileID: 4695782520769768438}
  - {fileID: 5343177940485436874}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5685816843826825054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4486933498449375600}
  - component: {fileID: 8585048686018416646}
  - component: {fileID: 4972805991378794194}
  - component: {fileID: 202161005440473754}
  m_Layer: 0
  m_Name: Wood_Floor_Piece4 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4486933498449375600
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5685816843826825054}
  m_LocalRotation: {x: 0.000000018864093, y: -0.5044042, z: 0.000000032879832, w: -0.86346775}
  m_LocalPosition: {x: 2.6159, y: 4.1504993, z: -0.6619105}
  m_LocalScale: {x: 0.9457439, y: 0.9457438, z: 0.9457537}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 69
  m_LocalEulerAnglesHint: {x: 0, y: -299.41602, z: 0}
--- !u!33 &8585048686018416646
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5685816843826825054}
  m_Mesh: {fileID: 2545581547402230364, guid: 79508dfc3e67c0b47858f8a36b60d149, type: 3}
--- !u!23 &4972805991378794194
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5685816843826825054}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &202161005440473754
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5685816843826825054}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.6829267, y: 0.097441405, z: 1.4208015}
  m_Center: {x: 0.20213023, y: -0.009633716, z: 0.020909818}
--- !u!1 &5692548620313592368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6335609705990960771}
  - component: {fileID: 8656799097063490271}
  - component: {fileID: 8313748910058979796}
  - component: {fileID: 781606176538716110}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6335609705990960771
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5692548620313592368}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 545922097178437189}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8656799097063490271
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5692548620313592368}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &8313748910058979796
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5692548620313592368}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &781606176538716110
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5692548620313592368}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079265, y: 3.011355, z: 0.31099522}
  m_Center: {x: 0.119674504, y: 1.4968746, z: 0.0008885642}
--- !u!1 &5789190107572011019
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1620596581651661233}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1620596581651661233
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5789190107572011019}
  m_LocalRotation: {x: -0.000000018768995, y: 0.5123629, z: -0.000000011198045, w: 0.8587691}
  m_LocalPosition: {x: 2.5489454, y: 5.739, z: -1.2621722}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1586021979562960686}
  - {fileID: 8477250068188026250}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 33
  m_LocalEulerAnglesHint: {x: 0, y: 61.643, z: 0}
--- !u!1 &5830652184183215717
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6936545065285266943}
  - component: {fileID: 5594692444906871629}
  - component: {fileID: 176799374927160700}
  - component: {fileID: 4705299286583924792}
  m_Layer: 0
  m_Name: Wood_Fence2 (14)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6936545065285266943
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5830652184183215717}
  m_LocalRotation: {x: 0.000000010945983, y: 0.86554587, z: -0.000000018917106, w: -0.5008298}
  m_LocalPosition: {x: -4.0378184, y: 4.161, z: -2.9581733}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 60
  m_LocalEulerAnglesHint: {x: 0, y: 240.11, z: 0}
--- !u!33 &5594692444906871629
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5830652184183215717}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &176799374927160700
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5830652184183215717}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4705299286583924792
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5830652184183215717}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996449, y: 0.8446262, z: 0.16607024}
  m_Center: {x: -0.0011022097, y: 0.42235386, z: -0.002608576}
--- !u!1 &5962560373041233625
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8478826543359794770}
  - component: {fileID: 3252254436870119654}
  - component: {fileID: 9179375019256569538}
  - component: {fileID: 5124600651260337107}
  m_Layer: 0
  m_Name: Wood_Fence2 (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8478826543359794770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5962560373041233625}
  m_LocalRotation: {x: 2.4641314e-10, y: 0.9999365, z: -0.000000021854307, w: -0.011274546}
  m_LocalPosition: {x: 1.276286, y: 4.1609993, z: -6.1644316}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 57
  m_LocalEulerAnglesHint: {x: 0, y: 181.292, z: 0}
--- !u!33 &3252254436870119654
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5962560373041233625}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &9179375019256569538
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5962560373041233625}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5124600651260337107
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5962560373041233625}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.399645, y: 0.8446262, z: 0.16607027}
  m_Center: {x: -0.0011022098, y: 0.42235386, z: -0.0026090534}
--- !u!1 &6151237163599044721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2237261565201535675}
  - component: {fileID: 445946237760825890}
  - component: {fileID: 8005766216311294887}
  - component: {fileID: 9128318225875861616}
  m_Layer: 0
  m_Name: Wood_Fence2 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2237261565201535675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6151237163599044721}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 0.0830003, y: 4.161, z: 1.3559997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 48
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &445946237760825890
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6151237163599044721}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &8005766216311294887
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6151237163599044721}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &9128318225875861616
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6151237163599044721}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996441, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022091, y: 0.42235386, z: -0.002608575}
--- !u!1 &6176368906576670659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8298637073956641249}
  - component: {fileID: 194028129869975340}
  - component: {fileID: 8874954134588123350}
  - component: {fileID: 1882418845083220326}
  m_Layer: 0
  m_Name: Wall3_CW1 (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8298637073956641249
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6176368906576670659}
  m_LocalRotation: {x: 0.000000010976276, y: 0.86474234, z: -0.000000018899543, w: -0.5022159}
  m_LocalPosition: {x: -2.2540002, y: 7.155, z: -3.5750003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 44
  m_LocalEulerAnglesHint: {x: 0, y: 240.293, z: 0}
--- !u!33 &194028129869975340
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6176368906576670659}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &8874954134588123350
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6176368906576670659}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1882418845083220326
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6176368906576670659}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007926, y: 3.0155451, z: 0.3109952}
  m_Center: {x: 0.11453652, y: 1.5006055, z: 0.00088807236}
--- !u!1 &6233813436341921615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7462148121626589291}
  - component: {fileID: 8324687139966740025}
  - component: {fileID: 1397798103255825951}
  - component: {fileID: 5688170734134928826}
  m_Layer: 0
  m_Name: Wall1_SW1 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7462148121626589291
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6233813436341921615}
  m_LocalRotation: {x: -0.0000000108698055, y: 0.8675533, z: -0.00000001896098, w: 0.49734434}
  m_LocalPosition: {x: 2.078565, y: 1.2400001, z: -3.7154133}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 28
  m_LocalEulerAnglesHint: {x: 0, y: 120.351, z: 0}
--- !u!33 &8324687139966740025
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6233813436341921615}
  m_Mesh: {fileID: 6375708778553226300, guid: 6f9a1c867ec13d8429a746ad701bd914, type: 3}
--- !u!23 &1397798103255825951
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6233813436341921615}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ce0d49ee954d64241908acdf028a06ab, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5688170734134928826
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6233813436341921615}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.9925408, y: 3.011355, z: 0.3109951}
  m_Center: {x: 0.12261394, y: 1.5053194, z: 0.000888102}
--- !u!1 &6279253910782071528
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5484278389149518175}
  - component: {fileID: 2804311045559045806}
  - component: {fileID: 1324584198962166893}
  - component: {fileID: 430108604701906915}
  m_Layer: 0
  m_Name: Wood_Fence2 (21)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5484278389149518175
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6279253910782071528}
  m_LocalRotation: {x: -0.000000011025913, y: -0.005188786, z: -0.00000001887063,
    w: 0.9999866}
  m_LocalPosition: {x: -1.2989069, y: 7.1483, z: -3.4376795}
  m_LocalScale: {x: 1.1008, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 83
  m_LocalEulerAnglesHint: {x: 0, y: -0.595, z: 0}
--- !u!33 &2804311045559045806
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6279253910782071528}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &1324584198962166893
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6279253910782071528}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &430108604701906915
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6279253910782071528}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022092, y: 0.42235386, z: -0.002608575}
--- !u!1 &6287307736136860899
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6596972807547847089}
  - component: {fileID: 3304462774204375526}
  - component: {fileID: 1726322520290716393}
  m_Layer: 0
  m_Name: Wood_Suport1 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6596972807547847089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6287307736136860899}
  m_LocalRotation: {x: 0.0000000061158523, y: -0.9600499, z: 0.000000020982558, w: -0.27982882}
  m_LocalPosition: {x: 1.6949997, y: 3.8968997, z: -5.538}
  m_LocalScale: {x: 0.8851001, y: 0.8851, z: 0.8851001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 76
  m_LocalEulerAnglesHint: {x: 0, y: -212.5, z: 0}
--- !u!33 &3304462774204375526
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6287307736136860899}
  m_Mesh: {fileID: 2441783984478029798, guid: 4590fc541dac1874a9ee25a7a0b25a88, type: 3}
--- !u!23 &1726322520290716393
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6287307736136860899}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37cd02b95341e4149b25001484bfd014, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6445267814407599908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3808405946260538358}
  - component: {fileID: 857361957432757415}
  - component: {fileID: 99403698058793738}
  - component: {fileID: 5696408878097691579}
  m_Layer: 0
  m_Name: Wood_Fence2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3808405946260538358
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6445267814407599908}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: -1.3259999, y: 4.161, z: 1.3559998}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &857361957432757415
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6445267814407599908}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &99403698058793738
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6445267814407599908}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5696408878097691579
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6445267814407599908}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996441, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022091, y: 0.42235386, z: -0.002608575}
--- !u!1 &6474094605314033129
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6602437169953551205}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6602437169953551205
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6474094605314033129}
  m_LocalRotation: {x: -0.000000019015257, y: -0.49298692, z: 0.0000000107745715,
    w: 0.8700367}
  m_LocalPosition: {x: -2.3902614, y: 5.739, z: -0.7009032}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2056715931942917457}
  - {fileID: 1361081118814869688}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: -59.074, z: 0}
--- !u!1 &6484620681328062707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 911324649808678380}
  - component: {fileID: 7147656464947598674}
  - component: {fileID: 8672297605866363507}
  - component: {fileID: 3506314406848241644}
  m_Layer: 0
  m_Name: Wood_Fence2 (15)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &911324649808678380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6484620681328062707}
  m_LocalRotation: {x: 0.000000019042673, y: 0.4907664, z: -0.0000000107260405, w: -0.87129116}
  m_LocalPosition: {x: -2.5102315, y: 4.161, z: 0.7667008}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 65
  m_LocalEulerAnglesHint: {x: 0, y: 301.218, z: 0}
--- !u!33 &7147656464947598674
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6484620681328062707}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &8672297605866363507
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6484620681328062707}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3506314406848241644
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6484620681328062707}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996441, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022687, y: 0.42235386, z: -0.0026088133}
--- !u!1 &6593151464764297690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 910552085028483228}
  - component: {fileID: 3379917368560149879}
  - component: {fileID: 8140316608754217344}
  - component: {fileID: 3890778826676200154}
  m_Layer: 0
  m_Name: Wall3_CW1 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &910552085028483228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6593151464764297690}
  m_LocalRotation: {x: 0.000000010976276, y: 0.86474234, z: -0.000000018899543, w: -0.5022159}
  m_LocalPosition: {x: -2.2540002, y: 10.117999, z: -3.5750005}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 36
  m_LocalEulerAnglesHint: {x: 0, y: 240.293, z: 0}
--- !u!33 &3379917368560149879
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6593151464764297690}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &8140316608754217344
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6593151464764297690}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3890778826676200154
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6593151464764297690}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007926, y: 3.0155451, z: 0.3109952}
  m_Center: {x: 0.1145364, y: 1.5006055, z: 0.00088807236}
--- !u!1 &6640001885486987132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7175193782749266275}
  - component: {fileID: 3108272395352593545}
  - component: {fileID: 1011819320604257140}
  - component: {fileID: 8193020555339237725}
  m_Layer: 0
  m_Name: Wood_Floor_Piece6 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7175193782749266275
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6640001885486987132}
  m_LocalRotation: {x: 4.440892e-16, y: -0.9659257, z: 1.7763568e-15, w: 0.25881958}
  m_LocalPosition: {x: -0.0025202946, y: 10.0427, z: -2.405587}
  m_LocalScale: {x: 0.9304618, y: 0.93046194, z: 0.9304618}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 87
  m_LocalEulerAnglesHint: {x: 0, y: -150, z: 0}
--- !u!33 &3108272395352593545
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6640001885486987132}
  m_Mesh: {fileID: 2860320775646373785, guid: b361263c1c27d844381325cdaef513b8, type: 3}
--- !u!23 &1011819320604257140
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6640001885486987132}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &8193020555339237725
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6640001885486987132}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 2860320775646373785, guid: b361263c1c27d844381325cdaef513b8, type: 3}
--- !u!1 &6688571506752467869
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8847710687903073269}
  - component: {fileID: 1971421654252254753}
  - component: {fileID: 2107883398053830910}
  - component: {fileID: 837584742948484017}
  m_Layer: 0
  m_Name: Box3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8847710687903073269
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6688571506752467869}
  m_LocalRotation: {x: -0.000000018861359, y: -0.50521225, z: 0.000000011041766, w: 0.86299515}
  m_LocalPosition: {x: -1.995, y: 1.293, z: 0.080000155}
  m_LocalScale: {x: 0.80000013, y: 0.8, z: 0.80000013}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: -60.691, z: 0}
--- !u!33 &1971421654252254753
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6688571506752467869}
  m_Mesh: {fileID: 429202423403544910, guid: 747584299864bdb4884f60fabc52cfbf, type: 3}
--- !u!23 &2107883398053830910
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6688571506752467869}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6f3895ddfcae6ba4295d9e485295f926, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &837584742948484017
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6688571506752467869}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.5210982, y: 0.32386017, z: 0.3883283}
  m_Center: {x: 0.0014647245, y: 0.15906832, z: -0.00012740488}
--- !u!1 &6702726384919461399
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8477250068188026250}
  - component: {fileID: 3535034572176315526}
  - component: {fileID: 2000178213365654795}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8477250068188026250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6702726384919461399}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1620596581651661233}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3535034572176315526
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6702726384919461399}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &2000178213365654795
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6702726384919461399}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6713840289599997643
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8092404939750605447}
  - component: {fileID: 7724059426655644332}
  - component: {fileID: 8248394204428365217}
  - component: {fileID: 598865454869644954}
  m_Layer: 0
  m_Name: Wall3_CW1 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8092404939750605447
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6713840289599997643}
  m_LocalRotation: {x: -0.0000000108108695, y: 0.86909354, z: -0.000000018994642,
    w: 0.49464774}
  m_LocalPosition: {x: 2.0819995, y: 10.117999, z: -3.700001}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 35
  m_LocalEulerAnglesHint: {x: 0, y: 120.707, z: 0}
--- !u!33 &7724059426655644332
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6713840289599997643}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &8248394204428365217
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6713840289599997643}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &598865454869644954
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6713840289599997643}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079243, y: 3.0155451, z: 0.310995}
  m_Center: {x: 0.114536695, y: 1.5006055, z: 0.0008880719}
--- !u!1 &6746318275636305307
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1431082456193842977}
  - component: {fileID: 4999036545686705074}
  - component: {fileID: 7366591230891531104}
  - component: {fileID: 2650159077909783929}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1431082456193842977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6746318275636305307}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4640775638103698599}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4999036545686705074
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6746318275636305307}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &7366591230891531104
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6746318275636305307}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2650159077909783929
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6746318275636305307}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007925, y: 3.011355, z: 0.31099507}
  m_Center: {x: 0.11967464, y: 1.4968741, z: 0.000888087}
--- !u!1 &6763036961680700900
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3659115740717765413}
  - component: {fileID: 1821842110805283559}
  - component: {fileID: 5187527919442993673}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3659115740717765413
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6763036961680700900}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8377976602938810851}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1821842110805283559
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6763036961680700900}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &5187527919442993673
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6763036961680700900}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6799485169544950112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3951332432746091754}
  - component: {fileID: 2401642635661796475}
  - component: {fileID: 2980823475013368365}
  - component: {fileID: 6248391234417109078}
  m_Layer: 0
  m_Name: Windmill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3951332432746091754
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6799485169544950112}
  m_LocalRotation: {x: 0.000000009888087, y: 0.000000030103607, z: -0.44362593, w: 0.8962121}
  m_LocalPosition: {x: -0.052155003, y: 11.629449, z: 0.18199953}
  m_LocalScale: {x: 1.0250664, y: 1.0250664, z: 1.0250664}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 29
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -52.671}
--- !u!33 &2401642635661796475
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6799485169544950112}
  m_Mesh: {fileID: -112859248700104106, guid: 43207d956b06dc143bb7dacd03889be7, type: 3}
--- !u!23 &2980823475013368365
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6799485169544950112}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d4e3562185ea0334a8315f5bef4373ca, type: 2}
  - {fileID: 2100000, guid: fe616c1bdabd59947a90fac90c4a84b6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &6248391234417109078
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6799485169544950112}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -112859248700104106, guid: 43207d956b06dc143bb7dacd03889be7, type: 3}
--- !u!1 &6826010762324157927
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1361081118814869688}
  - component: {fileID: 9017891648933298931}
  - component: {fileID: 796056723794413278}
  m_Layer: 0
  m_Name: Window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1361081118814869688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6826010762324157927}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.2639797, y: 0.0910691, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6602437169953551205}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &9017891648933298931
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6826010762324157927}
  m_Mesh: {fileID: -2984550300400590651, guid: bb606c30618cde14f937a1baf9401cf2, type: 3}
--- !u!23 &796056723794413278
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6826010762324157927}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1eed69dcc0a4f7f4997da58fb8c8aace, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6854259005085541007
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1586021979562960686}
  - component: {fileID: 9203091442753422987}
  - component: {fileID: 4302108740479635412}
  - component: {fileID: 5729046133078064685}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1586021979562960686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6854259005085541007}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1620596581651661233}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &9203091442753422987
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6854259005085541007}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &4302108740479635412
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6854259005085541007}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5729046133078064685
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6854259005085541007}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007925, y: 3.011355, z: 0.31099507}
  m_Center: {x: 0.1196744, y: 1.4968741, z: 0.00088796776}
--- !u!1 &6864932743750484810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3928528397785229217}
  - component: {fileID: 1020410423751426819}
  - component: {fileID: 5846892974055332676}
  - component: {fileID: 2737340236900624105}
  m_Layer: 0
  m_Name: Wood_Fence2 (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3928528397785229217
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6864932743750484810}
  m_LocalRotation: {x: -0.000000010863008, y: 0.8677315, z: -0.000000018964874, w: 0.49703333}
  m_LocalPosition: {x: 2.4426908, y: 4.1609993, z: -5.569064}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 53
  m_LocalEulerAnglesHint: {x: 0, y: 120.392, z: 0}
--- !u!33 &1020410423751426819
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6864932743750484810}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &5846892974055332676
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6864932743750484810}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2737340236900624105
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6864932743750484810}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996449, y: 0.8446262, z: 0.16607024}
  m_Center: {x: -0.0011022097, y: 0.42235386, z: -0.002608576}
--- !u!1 &7064180222130574129
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3375618895100984472}
  - component: {fileID: 4118478589239196986}
  - component: {fileID: 2614898090742673432}
  - component: {fileID: 652459135750497453}
  m_Layer: 0
  m_Name: Wood_Floor_Piece5 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3375618895100984472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7064180222130574129}
  m_LocalRotation: {x: -1.7763568e-15, y: -0.25817484, z: -8.881784e-16, w: 0.96609825}
  m_LocalPosition: {x: -0.060000155, y: 4.236, z: -2.4120002}
  m_LocalScale: {x: 0.93928593, y: 0.93928593, z: 0.93928593}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 72
  m_LocalEulerAnglesHint: {x: 0, y: -29.924, z: 0}
--- !u!33 &4118478589239196986
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7064180222130574129}
  m_Mesh: {fileID: 551321949575145288, guid: 6235f8f6a81c9a945870b1e8ac3569f2, type: 3}
--- !u!23 &2614898090742673432
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7064180222130574129}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &652459135750497453
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7064180222130574129}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.6520383, y: 0.0974417, z: 6.0996866}
  m_Center: {x: 1.3370308, y: -0.008200743, z: 0.0011667014}
--- !u!1 &7181951236153767961
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4058092835216825312}
  - component: {fileID: 7813565368962264682}
  - component: {fileID: 2865941931989841667}
  - component: {fileID: 7590230528267448033}
  m_Layer: 0
  m_Name: Stone base stairs
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4058092835216825312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7181951236153767961}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: -0.0045708404, y: 0, z: 1.381}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7813565368962264682
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7181951236153767961}
  m_Mesh: {fileID: 4794247262882543407, guid: 7de0e7a593c0555468f9066cb52e250c, type: 3}
--- !u!23 &2865941931989841667
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7181951236153767961}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cac3b32406ac56642a6f0d9cffa5b3f8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &7590230528267448033
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7181951236153767961}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 4794247262882543407, guid: 7de0e7a593c0555468f9066cb52e250c, type: 3}
--- !u!1 &7485488576967575256
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6418666949583980486}
  - component: {fileID: 3177178550593166098}
  - component: {fileID: 3761611260744223924}
  - component: {fileID: 9094867439258320574}
  m_Layer: 0
  m_Name: Wood_Floor_Piece4 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6418666949583980486
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7485488576967575256}
  m_LocalRotation: {x: -3.3017145e-11, y: -0.49632466, z: -5.7601035e-11, w: 0.868137}
  m_LocalPosition: {x: -2.868, y: 4.1504993, z: -0.95723003}
  m_LocalScale: {x: 0.9457438, y: 0.9457438, z: 0.94575363}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 66
  m_LocalEulerAnglesHint: {x: 0, y: -59.514, z: 0}
--- !u!33 &3177178550593166098
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7485488576967575256}
  m_Mesh: {fileID: 2545581547402230364, guid: 79508dfc3e67c0b47858f8a36b60d149, type: 3}
--- !u!23 &3761611260744223924
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7485488576967575256}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &9094867439258320574
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7485488576967575256}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.682926, y: 0.097441405, z: 1.4208014}
  m_Center: {x: 0.2021302, y: -0.009633716, z: 0.020909816}
--- !u!1 &7616620062368931321
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6665357224440564549}
  - component: {fileID: 5460298138848480878}
  - component: {fileID: 7624219752111407655}
  - component: {fileID: 7375790370238133466}
  m_Layer: 0
  m_Name: Wall3_Door1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6665357224440564549
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7616620062368931321}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: 0.53735876, y: -1.6757565, z: 0.80293614}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 830962498211775844}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5460298138848480878
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7616620062368931321}
  m_Mesh: {fileID: 8519133855382833671, guid: 85bf0254f1586c842acb4a72168bc0bd, type: 3}
--- !u!23 &7624219752111407655
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7616620062368931321}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  - {fileID: 2100000, guid: 611b6187a8b10454da80524157a82496, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &7375790370238133466
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7616620062368931321}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 8519133855382833671, guid: 85bf0254f1586c842acb4a72168bc0bd, type: 3}
--- !u!1 &7648746718302153454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3775488179122636188}
  - component: {fileID: 2224753400185172609}
  - component: {fileID: 4596568185341369961}
  - component: {fileID: 6973274520948388207}
  m_Layer: 0
  m_Name: Wall3_CW1 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3775488179122636188
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7648746718302153454}
  m_LocalRotation: {x: 0.000000010976276, y: 0.86474234, z: -0.000000018899543, w: -0.5022159}
  m_LocalPosition: {x: -2.2540002, y: 4.207, z: -3.5750003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 32
  m_LocalEulerAnglesHint: {x: 0, y: 240.293, z: 0}
--- !u!33 &2224753400185172609
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7648746718302153454}
  m_Mesh: {fileID: -326155976204738199, guid: d52f5ca5c6630944998a99f34215f874, type: 3}
--- !u!23 &4596568185341369961
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7648746718302153454}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6973274520948388207
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7648746718302153454}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007926, y: 3.0155451, z: 0.3109952}
  m_Center: {x: 0.1145364, y: 1.5006055, z: 0.00088807236}
--- !u!1 &7682454210051841122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2241021225350040009}
  - component: {fileID: 5906993123486295970}
  - component: {fileID: 5980531312089596381}
  - component: {fileID: 1309156029290372758}
  m_Layer: 0
  m_Name: Wall1_SW1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2241021225350040009
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7682454210051841122}
  m_LocalRotation: {x: 0.00000001089978, y: 0.8667656, z: -0.000000018943764, w: -0.4987158}
  m_LocalPosition: {x: -2.2110002, y: 1.2400001, z: -3.6309998}
  m_LocalScale: {x: 1.0246664, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 27
  m_LocalEulerAnglesHint: {x: 0, y: 239.83, z: 0}
--- !u!33 &5906993123486295970
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7682454210051841122}
  m_Mesh: {fileID: 6375708778553226300, guid: 6f9a1c867ec13d8429a746ad701bd914, type: 3}
--- !u!23 &5980531312089596381
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7682454210051841122}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ce0d49ee954d64241908acdf028a06ab, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1309156029290372758
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7682454210051841122}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.9925408, y: 3.011355, z: 0.3109951}
  m_Center: {x: 0.12261394, y: 1.5053194, z: 0.000888102}
--- !u!1 &7719635246782432729
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1907422909260600636}
  - component: {fileID: 5230619555932885891}
  - component: {fileID: 3785752027489472689}
  - component: {fileID: 192357678824700050}
  m_Layer: 0
  m_Name: Wood_Floor_Piece4 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1907422909260600636
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7719635246782432729}
  m_LocalRotation: {x: -1.0658141e-14, y: -0.86720926, z: 0.000000037906936, w: -0.49794394}
  m_LocalPosition: {x: 2.7769, y: 4.1504993, z: -3.8286004}
  m_LocalScale: {x: 0.94574404, y: 0.9457438, z: 0.94575375}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 70
  m_LocalEulerAnglesHint: {x: 0, y: -239.728, z: 0}
--- !u!33 &5230619555932885891
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7719635246782432729}
  m_Mesh: {fileID: 2545581547402230364, guid: 79508dfc3e67c0b47858f8a36b60d149, type: 3}
--- !u!23 &3785752027489472689
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7719635246782432729}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93e2bcaafb8a5954c97f18357884e2b8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &192357678824700050
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7719635246782432729}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.6829267, y: 0.097441405, z: 1.4208015}
  m_Center: {x: 0.20213023, y: -0.009633716, z: 0.020909818}
--- !u!1 &7744725075554312615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2056715931942917457}
  - component: {fileID: 201696045291695131}
  - component: {fileID: 5122451772251303313}
  - component: {fileID: 5500405123255692576}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2056715931942917457
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7744725075554312615}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6602437169953551205}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &201696045291695131
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7744725075554312615}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &5122451772251303313
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7744725075554312615}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5500405123255692576
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7744725075554312615}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007925, y: 3.011355, z: 0.31099507}
  m_Center: {x: 0.1196744, y: 1.4968741, z: 0.000888087}
--- !u!1 &7830074340583401752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3656371530000266318}
  - component: {fileID: 8686279285850303965}
  - component: {fileID: 6221810303661336320}
  - component: {fileID: 8853902222194643057}
  m_Layer: 0
  m_Name: Wood_Fence2 (18)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3656371530000266318
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7830074340583401752}
  m_LocalRotation: {x: -0.000000018984034, y: 0.49549967, z: -0.000000010829489, w: 0.8686082}
  m_LocalPosition: {x: -0.7830003, y: 4.26, z: -3.1520002}
  m_LocalScale: {x: 1.1008003, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 59.405, z: 0}
--- !u!33 &8686279285850303965
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7830074340583401752}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &6221810303661336320
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7830074340583401752}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8853902222194643057
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7830074340583401752}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022091, y: 0.42235386, z: -0.002608575}
--- !u!1 &7938339819838081667
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3910593276427218382}
  - component: {fileID: 2726941319544045828}
  - component: {fileID: 4442292364104629497}
  - component: {fileID: 928132076149876509}
  m_Layer: 0
  m_Name: Wall3_window3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3910593276427218382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7938339819838081667}
  m_LocalRotation: {x: 0.000000021855694, y: -0.00000004371139, z: 1.0165751e-15,
    w: 1}
  m_LocalPosition: {x: -0.3009797, y: -1.5369309, z: -0.32142466}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3160055184589488195}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2726941319544045828
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7938339819838081667}
  m_Mesh: {fileID: -6789057103910968456, guid: d76eaacb6ed35b443b238ec7e4677a2d, type: 3}
--- !u!23 &4442292364104629497
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7938339819838081667}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 05b9291e210fcdd45b104b8f62fe0d5f, type: 2}
  - {fileID: 2100000, guid: 78aa2e3a70373a842bedf5383be4814f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &928132076149876509
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7938339819838081667}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.007925, y: 3.011355, z: 0.31099507}
  m_Center: {x: 0.1196744, y: 1.4968741, z: 0.00088796776}
--- !u!1 &7951061733112970290
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3197412588685102085}
  - component: {fileID: 6679804644594163366}
  - component: {fileID: 3849652088995841185}
  m_Layer: 0
  m_Name: Wood_Suport1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3197412588685102085
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7951061733112970290}
  m_LocalRotation: {x: -0.000000021226201, y: 0.23827536, z: -0.0000000052076734,
    w: 0.9711977}
  m_LocalPosition: {x: 1.7750001, y: 3.8969, z: 0.63899964}
  m_LocalScale: {x: 0.88510007, y: 0.8851, z: 0.88510007}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 27.57, z: 0}
--- !u!33 &6679804644594163366
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7951061733112970290}
  m_Mesh: {fileID: 2441783984478029798, guid: 4590fc541dac1874a9ee25a7a0b25a88, type: 3}
--- !u!23 &3849652088995841185
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7951061733112970290}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37cd02b95341e4149b25001484bfd014, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7978433671000554034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 830962498211775844}
  m_Layer: 0
  m_Name: Wall3_CW1_Door1_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &830962498211775844
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7978433671000554034}
  m_LocalRotation: {x: -0.000000021855628, y: 0.0024565274, z: -5.3689168e-11, w: 0.999997}
  m_LocalPosition: {x: -0.5329465, y: 5.869, z: -0.68000525}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6665357224440564549}
  - {fileID: 4175548612110169461}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0.281, z: 0}
--- !u!1 &8021935389100701616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3160055184589488195}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3160055184589488195
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8021935389100701616}
  m_LocalRotation: {x: -0.000000018768995, y: 0.5123629, z: -0.000000011198045, w: 0.8587691}
  m_LocalPosition: {x: 2.5489454, y: 8.687, z: -1.2621723}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3910593276427218382}
  - {fileID: 3878186436841578867}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 42
  m_LocalEulerAnglesHint: {x: 0, y: 61.643, z: 0}
--- !u!1 &8067977736292802927
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5760419385270443054}
  - component: {fileID: 7370398190880672485}
  - component: {fileID: 242775232934501984}
  - component: {fileID: 7274878495191799370}
  m_Layer: 0
  m_Name: Wood_Fence2 (23)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5760419385270443054
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8067977736292802927}
  m_LocalRotation: {x: -1.134044e-10, y: -0.5044869, z: -0.000000021855396, w: 0.86341935}
  m_LocalPosition: {x: 0.23877704, y: 10.05, z: -4.018324}
  m_LocalScale: {x: 1.1008002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 84
  m_LocalEulerAnglesHint: {x: 0, y: -60.595, z: 0}
--- !u!33 &7370398190880672485
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8067977736292802927}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &242775232934501984
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8067977736292802927}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7274878495191799370
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8067977736292802927}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607016}
  m_Center: {x: -0.0011022092, y: 0.42235386, z: -0.002608575}
--- !u!1 &8084905079139355976
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4515221368256528461}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4515221368256528461
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8084905079139355976}
  m_LocalRotation: {x: 2.1265521e-10, y: 0.99995273, z: -0.00000002185466, w: -0.009729966}
  m_LocalPosition: {x: -0.3790004, y: 8.687, z: -5.2470007}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5845760967674957729}
  - {fileID: 5644942449178350806}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 41
  m_LocalEulerAnglesHint: {x: 0, y: 181.115, z: 0}
--- !u!1 &8306262847399238368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6511638242502295910}
  - component: {fileID: 7177080858935692554}
  - component: {fileID: 4942007307820358003}
  - component: {fileID: 7877707784124925062}
  m_Layer: 0
  m_Name: Wood_Fence2 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6511638242502295910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8306262847399238368}
  m_LocalRotation: {x: -0.000000018879541, y: 0.5037885, z: -0.000000011010646, w: 0.8638271}
  m_LocalPosition: {x: 3.9553034, y: 4.161, z: -1.8040005}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 52
  m_LocalEulerAnglesHint: {x: 0, y: 60.502, z: 0}
--- !u!33 &7177080858935692554
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8306262847399238368}
  m_Mesh: {fileID: -2471856243978033659, guid: 390c6e86909ddd440a5b37a606238052, type: 3}
--- !u!23 &4942007307820358003
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8306262847399238368}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3893bc758d14a264aabb7b5dcd09415d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7877707784124925062
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8306262847399238368}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.3996443, y: 0.8446262, z: 0.16607018}
  m_Center: {x: -0.0011022092, y: 0.42235386, z: -0.0026088136}
--- !u!1 &8476594397139464561
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7090919684701402568}
  - component: {fileID: 3170026398750135745}
  - component: {fileID: 8095705700766879559}
  - component: {fileID: 5042167985887622361}
  m_Layer: 0
  m_Name: Box1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7090919684701402568
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8476594397139464561}
  m_LocalRotation: {x: -0.0000000061063585, y: -0.96017647, z: 0.000000020985324,
    w: 0.2793944}
  m_LocalPosition: {x: 1.687, y: 1.784, z: -3.2880003}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 89
  m_LocalEulerAnglesHint: {x: 0, y: -147.552, z: 0}
--- !u!33 &3170026398750135745
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8476594397139464561}
  m_Mesh: {fileID: 1850462315688071848, guid: 3cedb26e22742574ea0002576e656359, type: 3}
--- !u!23 &8095705700766879559
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8476594397139464561}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b42e40c611a2d12488635e75af670899, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5042167985887622361
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8476594397139464561}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0368521, y: 0.98749745, z: 1.048191}
  m_Center: {x: -0.000068157926, y: -0.001739502, z: 0.0067923083}
--- !u!1 &8569581801792284848
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7578924180870275081}
  m_Layer: 0
  m_Name: Wall3_CW1_Window3_1 (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7578924180870275081
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8569581801792284848}
  m_LocalRotation: {x: -0.000000019015257, y: -0.49298692, z: 0.0000000107745715,
    w: 0.8700367}
  m_LocalPosition: {x: -2.3902614, y: 8.687, z: -0.7009033}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4379947272523327790}
  - {fileID: 292248333504136684}
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 43
  m_LocalEulerAnglesHint: {x: 0, y: -59.074, z: 0}
--- !u!1 &8664900547520146900
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4796419371448874848}
  - component: {fileID: 8823324707802764677}
  - component: {fileID: 2459651022692217985}
  - component: {fileID: 8621934995114141540}
  m_Layer: 0
  m_Name: Box1 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4796419371448874848
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664900547520146900}
  m_LocalRotation: {x: 0.000000010372777, y: -0.8802, z: 0.000000019237383, w: -0.474603}
  m_LocalPosition: {x: -2.9927974, y: 0.41300002, z: 0.37434167}
  m_LocalScale: {x: 0.8000001, y: 0.8, z: 0.8000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 93
  m_LocalEulerAnglesHint: {x: 0, y: -236.66699, z: 0}
--- !u!33 &8823324707802764677
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664900547520146900}
  m_Mesh: {fileID: 1850462315688071848, guid: 3cedb26e22742574ea0002576e656359, type: 3}
--- !u!23 &2459651022692217985
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664900547520146900}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b42e40c611a2d12488635e75af670899, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8621934995114141540
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664900547520146900}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0368521, y: 0.98749745, z: 1.048191}
  m_Center: {x: -0.00006803851, y: -0.001739502, z: 0.0067925467}
--- !u!1 &8770837708060888343
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4852979484439943246}
  - component: {fileID: 4322810286267760080}
  - component: {fileID: 5966814077499575477}
  - component: {fileID: 6629891782592752997}
  m_Layer: 0
  m_Name: Barrel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4852979484439943246
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8770837708060888343}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 1.9410001, y: 0.010000039, z: 1.1159999}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4322810286267760080
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8770837708060888343}
  m_Mesh: {fileID: 8108563764783910780, guid: b296b627c932b3a45b5a5088d224ce88, type: 3}
--- !u!23 &5966814077499575477
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8770837708060888343}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 12bd68aa68eb95c4c8be8c02d32b5dcf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &6629891782592752997
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8770837708060888343}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.50353736
  m_Height: 1.1603228
  m_Direction: 1
  m_Center: {x: -0.00000014901161, y: 0.580162, z: 0.000605464}
--- !u!1 &9036628337075249032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5615303026752057446}
  - component: {fileID: 4505351445109228547}
  - component: {fileID: 8919784067343124230}
  m_Layer: 0
  m_Name: Wood_Suport1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5615303026752057446
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9036628337075249032}
  m_LocalRotation: {x: -0.000000015151901, y: -0.72067773, z: 0.000000015750915, w: 0.6932703}
  m_LocalPosition: {x: -3.6120224, y: 3.8969, z: -2.3317227}
  m_LocalScale: {x: 0.8851004, y: 0.8851, z: 0.8851004}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 74
  m_LocalEulerAnglesHint: {x: 0, y: -92.221, z: 0}
--- !u!33 &4505351445109228547
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9036628337075249032}
  m_Mesh: {fileID: 2441783984478029798, guid: 4590fc541dac1874a9ee25a7a0b25a88, type: 3}
--- !u!23 &8919784067343124230
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9036628337075249032}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37cd02b95341e4149b25001484bfd014, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &9114165114508145113
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4954324626672949676}
  - component: {fileID: 3209615283276937577}
  - component: {fileID: 554748738224924330}
  m_Layer: 0
  m_Name: Wood_Suport1 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4954324626672949676
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9114165114508145113}
  m_LocalRotation: {x: 0.00000001599078, y: -0.6816775, z: 0.000000014898534, w: -0.7316528}
  m_LocalPosition: {x: 3.4949996, y: 3.8969, z: -2.4800005}
  m_LocalScale: {x: 0.8851, y: 0.8851, z: 0.8851}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 77
  m_LocalEulerAnglesHint: {x: 0, y: -274.05, z: 0}
--- !u!33 &3209615283276937577
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9114165114508145113}
  m_Mesh: {fileID: 2441783984478029798, guid: 4590fc541dac1874a9ee25a7a0b25a88, type: 3}
--- !u!23 &554748738224924330
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9114165114508145113}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37cd02b95341e4149b25001484bfd014, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &9143827291718227641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8632464480615673788}
  - component: {fileID: 163639780037445036}
  - component: {fileID: 4790389736238157353}
  - component: {fileID: 7112619156776604587}
  m_Layer: 0
  m_Name: Box1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8632464480615673788
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9143827291718227641}
  m_LocalRotation: {x: -0.0000000061063585, y: -0.96017647, z: 0.000000020985324,
    w: 0.2793944}
  m_LocalPosition: {x: 2.0409997, y: 2.552, z: -2.6170003}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 90
  m_LocalEulerAnglesHint: {x: 0, y: -147.552, z: 0}
--- !u!33 &163639780037445036
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9143827291718227641}
  m_Mesh: {fileID: 1850462315688071848, guid: 3cedb26e22742574ea0002576e656359, type: 3}
--- !u!23 &4790389736238157353
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9143827291718227641}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b42e40c611a2d12488635e75af670899, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7112619156776604587
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9143827291718227641}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0368521, y: 0.98749745, z: 1.048191}
  m_Center: {x: -0.000068396344, y: -0.001739502, z: 0.0067924275}
--- !u!1 &9147876816705161001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2031982481864873830}
  - component: {fileID: 5010213370838391381}
  - component: {fileID: 9034565320196142567}
  m_Layer: 0
  m_Name: Torch3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2031982481864873830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9147876816705161001}
  m_LocalRotation: {x: -0.000000021855694, y: 0.00000004371139, z: -1.016575e-15,
    w: 1}
  m_LocalPosition: {x: 0.98, y: 2.847, z: 0.18739977}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1035774684743862908}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5010213370838391381
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9147876816705161001}
  m_Mesh: {fileID: -8736361502420416377, guid: 6cd38d18fcd2bc04ca0235fb1f094112, type: 3}
--- !u!23 &9034565320196142567
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9147876816705161001}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: df3eb4d9c0f5146448b7660b8ecbca95, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1001 &1556372180734983977
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 3773828200412994835, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_Name
      value: Stone_Column1 (13)
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_RootOrder
      value: 99
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9618662
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.8649995
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.2639999
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.5320017
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.999938
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 5.3290705e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.011135222
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -1.3322676e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -1.276
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 646c19500c61d054ebc71b37cf8b63b3, type: 3}
--- !u!4 &3066349006893725824 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
    type: 3}
  m_PrefabInstance: {fileID: 1556372180734983977}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2395816097789328328
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_RootOrder
      value: 102
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.3788
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.46
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8048733606225234233, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_Name
      value: Mill_Piece4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e09608bbc0d27de4295687023192b6e6, type: 3}
--- !u!4 &5027897566589134923 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
    type: 3}
  m_PrefabInstance: {fileID: 2395816097789328328}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2670410777935643348
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 961448279462811577, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_Name
      value: Mill
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_RootOrder
      value: 101
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.038150642
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.4065598
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.4652998
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9079241
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.41913468
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -49.56
      objectReference: {fileID: 0}
    - target: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 364ecda3409d2084fa7a343f7a3cb290, type: 3}
--- !u!4 &2094869388397618322 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4043472600527873606, guid: 364ecda3409d2084fa7a343f7a3cb290,
    type: 3}
  m_PrefabInstance: {fileID: 2670410777935643348}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2725748065318598773
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_RootOrder
      value: 104
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 10.311
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.46
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8048733606225234233, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_Name
      value: Mill_Piece4 (2)
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e09608bbc0d27de4295687023192b6e6, type: 3}
--- !u!4 &4695782520769768438 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
    type: 3}
  m_PrefabInstance: {fileID: 2725748065318598773}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2768489811174516447
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_RootOrder
      value: 103
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 7.357
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.46
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8048733606225234233, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_Name
      value: Mill_Piece4 (1)
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e09608bbc0d27de4295687023192b6e6, type: 3}
--- !u!4 &4797088890716812636 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
    type: 3}
  m_PrefabInstance: {fileID: 2768489811174516447}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3377629599586051657
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_RootOrder
      value: 105
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.7646846
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 13.289
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.46
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8048733606225234233, guid: e09608bbc0d27de4295687023192b6e6,
        type: 3}
      propertyPath: m_Name
      value: Mill_Piece4 (3)
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e09608bbc0d27de4295687023192b6e6, type: 3}
--- !u!4 &5343177940485436874 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7275855738469979011, guid: e09608bbc0d27de4295687023192b6e6,
    type: 3}
  m_PrefabInstance: {fileID: 3377629599586051657}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3552432440609091642
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 3773828200412994835, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_Name
      value: Stone_Column1 (11)
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_RootOrder
      value: 97
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9618662
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.9680007
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.2639999
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.312002
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7080038
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 1.7763568e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7062086
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 5.3290705e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 89.855
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 646c19500c61d054ebc71b37cf8b63b3, type: 3}
--- !u!4 &1033648784343807891 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
    type: 3}
  m_PrefabInstance: {fileID: 3552432440609091642}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3859171334406873459
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 3773828200412994835, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_Name
      value: Stone_Column1 (12)
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_RootOrder
      value: 98
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9618662
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.536
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.26
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -4.901
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9544848
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.2982597
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 3.1086245e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 34.706
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 646c19500c61d054ebc71b37cf8b63b3, type: 3}
--- !u!4 &763926993752729306 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
    type: 3}
  m_PrefabInstance: {fileID: 3859171334406873459}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4167090364152950859
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 3773828200412994835, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_Name
      value: Stone_Column1 (10)
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_RootOrder
      value: 96
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9618662
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.4730005
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.264
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.1919976
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.878956
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 1.7763568e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.47690296
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 8.881784e-16
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 56.967
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 646c19500c61d054ebc71b37cf8b63b3, type: 3}
--- !u!4 &486669099989841890 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
    type: 3}
  m_PrefabInstance: {fileID: 4167090364152950859}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6553320368923413171
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 3773828200412994835, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_Name
      value: Stone_Column1 (14)
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_RootOrder
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9618662
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.3559995
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.2639998
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -5.005003
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.972428
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 3.5527137e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.23320334
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -1.7763568e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -26.971
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 646c19500c61d054ebc71b37cf8b63b3, type: 3}
--- !u!4 &7342721495556459802 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
    type: 3}
  m_PrefabInstance: {fileID: 6553320368923413171}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &9118983897063147102
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1035774684743862908}
    m_Modifications:
    - target: {fileID: 3773828200412994835, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_Name
      value: Stone_Column1 (9)
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_RootOrder
      value: 95
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9618662
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.5219994
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.264
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.1209982
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9659259
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 1.7763568e-15
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.258819
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 4.440892e-16
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 646c19500c61d054ebc71b37cf8b63b3, type: 3}
--- !u!4 &4726993891573842423 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4545403525098080169, guid: 646c19500c61d054ebc71b37cf8b63b3,
    type: 3}
  m_PrefabInstance: {fileID: 9118983897063147102}
  m_PrefabAsset: {fileID: 0}
