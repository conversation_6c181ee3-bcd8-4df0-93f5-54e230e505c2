%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1672090869842656987
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3883643503869329969}
  - component: {fileID: 1030253980343535229}
  - component: {fileID: 4647108355035281753}
  - component: {fileID: 78556249591710927}
  m_Layer: 0
  m_Name: "\u7A7A\u6C14\u58993"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3883643503869329969
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1672090869842656987}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -534.17, y: 102.47948, z: 2195.76}
  m_LocalScale: {x: 17.367554, y: 6.8857026, z: 13.072553}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1030253980343535229
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1672090869842656987}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4647108355035281753
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1672090869842656987}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &78556249591710927
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1672090869842656987}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1776361768807349714
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9184457481772214615}
  - component: {fileID: 4036278817366352588}
  - component: {fileID: 5859828743132425437}
  - component: {fileID: 5971744697372452604}
  m_Layer: 0
  m_Name: "\u7A7A\u6C14\u58992"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9184457481772214615
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776361768807349714}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -611.4597, y: 28.04089, z: 2367.4097}
  m_LocalScale: {x: 17.367554, y: 6.8857026, z: 3.5343642}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4036278817366352588
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776361768807349714}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5859828743132425437
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776361768807349714}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5971744697372452604
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1776361768807349714}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &2464005686873156254
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3085158441743111458}
  - component: {fileID: 7978448175433640459}
  - component: {fileID: 2634859353881312106}
  - component: {fileID: 7676822555476948888}
  - component: {fileID: 7885614673597500584}
  m_Layer: 0
  m_Name: "\u63D0\u9192\u89E6\u53D1\u56682_1"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3085158441743111458
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2464005686873156254}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -536.874, y: 101.516174, z: 2190.7417}
  m_LocalScale: {x: 16.039948, y: 4.959083, z: 6.5355477}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7978448175433640459
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2464005686873156254}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2634859353881312106
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2464005686873156254}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7676822555476948888
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2464005686873156254}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7885614673597500584
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2464005686873156254}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cee5a9299ac601947ad7222dbec22a9d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onlyNormalToTrigger: 1
  enterEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 5007548558710176885}
        m_TargetAssemblyTypeName: Temp_SimpleAttention, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  stayEvent:
    m_PersistentCalls:
      m_Calls: []
  exitEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &3047841012317060082
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2928969001759703717}
  - component: {fileID: 2799007501634581692}
  - component: {fileID: 3780869361403815206}
  - component: {fileID: 1912995398778594327}
  - component: {fileID: 8605378319507469027}
  m_Layer: 0
  m_Name: "\u63D0\u9192\u89E6\u53D1\u56682"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2928969001759703717
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3047841012317060082}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -933.42, y: 39.8731, z: 2144.5715}
  m_LocalScale: {x: 1.8898, y: 4.959083, z: 26.019896}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2799007501634581692
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3047841012317060082}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3780869361403815206
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3047841012317060082}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1912995398778594327
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3047841012317060082}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &8605378319507469027
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3047841012317060082}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cee5a9299ac601947ad7222dbec22a9d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onlyNormalToTrigger: 1
  enterEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 5007548558710176885}
        m_TargetAssemblyTypeName: Temp_SimpleAttention, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  stayEvent:
    m_PersistentCalls:
      m_Calls: []
  exitEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &3272989323574588691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7111593626127432415}
  - component: {fileID: 1309140988732921904}
  - component: {fileID: 7145507811778193607}
  - component: {fileID: 5059262904332405216}
  m_Layer: 0
  m_Name: "\u7A7A\u6C14\u58994"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7111593626127432415
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3272989323574588691}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -724.6422, y: -14.9892, z: 2421.95}
  m_LocalScale: {x: 8.27429, y: 2.913178, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1309140988732921904
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3272989323574588691}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7145507811778193607
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3272989323574588691}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5059262904332405216
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3272989323574588691}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &3828440910532555158
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6369008997594043435}
  - component: {fileID: 227732667564917551}
  - component: {fileID: 8022920245332688761}
  - component: {fileID: 2585885949641214569}
  m_Layer: 0
  m_Name: "\u7A7A\u6C14\u58991"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6369008997594043435
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3828440910532555158}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -934.8, y: 40.836372, z: 2144.5715}
  m_LocalScale: {x: 1.8898, y: 6.8857026, z: 26.019896}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &227732667564917551
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3828440910532555158}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8022920245332688761
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3828440910532555158}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2585885949641214569
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3828440910532555158}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &4793500538661838992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3689438207306604367}
  - component: {fileID: 7696976838616943565}
  - component: {fileID: 2960140441052693382}
  - component: {fileID: 5888797879860800993}
  - component: {fileID: 4212140948695173595}
  m_Layer: 0
  m_Name: "\u63D0\u9192\u89E6\u53D1\u56683"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3689438207306604367
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4793500538661838992}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -725.6415, y: -15.1607, z: 2424.3972}
  m_LocalScale: {x: 10.56327, y: 2.0343, z: 7.248001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7696976838616943565
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4793500538661838992}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2960140441052693382
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4793500538661838992}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5888797879860800993
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4793500538661838992}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &4212140948695173595
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4793500538661838992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cee5a9299ac601947ad7222dbec22a9d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onlyNormalToTrigger: 1
  enterEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 5007548558710176885}
        m_TargetAssemblyTypeName: Temp_SimpleAttention, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  stayEvent:
    m_PersistentCalls:
      m_Calls: []
  exitEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &5361846942671344065
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5333410879299271017}
  - component: {fileID: 7450814959267178254}
  - component: {fileID: 6629507850895481308}
  - component: {fileID: 6564303371607829081}
  - component: {fileID: 9168768439261573971}
  m_Layer: 0
  m_Name: "\u63D0\u9192\u89E6\u53D1\u56681"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5333410879299271017
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5361846942671344065}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -610.9066, y: 25.6097, z: 2364.8708}
  m_LocalScale: {x: 15.400656, y: 2.0343, z: 2.3182862}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7450814959267178254
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5361846942671344065}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6629507850895481308
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5361846942671344065}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6564303371607829081
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5361846942671344065}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &9168768439261573971
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5361846942671344065}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cee5a9299ac601947ad7222dbec22a9d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onlyNormalToTrigger: 1
  enterEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 5007548558710176885}
        m_TargetAssemblyTypeName: Temp_SimpleAttention, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  stayEvent:
    m_PersistentCalls:
      m_Calls: []
  exitEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &7205189894705339502
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8711657461035949419}
  - component: {fileID: 9028684693389029647}
  m_Layer: 0
  m_Name: "\u9690\u85CF\u5185\u5BB9"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8711657461035949419
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7205189894705339502}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9028684693389029647
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7205189894705339502}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fa671c408e8041b4688b745643e446e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  unityEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!1 &8298621446965684642
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3921500894284969093}
  m_Layer: 0
  m_Name: "\u7248\u672C\u533A\u57DF\u63A7\u5236"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3921500894284969093
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8298621446965684642}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6369008997594043435}
  - {fileID: 9184457481772214615}
  - {fileID: 3883643503869329969}
  - {fileID: 7111593626127432415}
  - {fileID: 8711657461035949419}
  - {fileID: 5333410879299271017}
  - {fileID: 2928969001759703717}
  - {fileID: 3085158441743111458}
  - {fileID: 3689438207306604367}
  - {fileID: 6104174667940741851}
  - {fileID: 3591028807710448123}
  - {fileID: 6989819189228358745}
  - {fileID: 693377650159074312}
  m_Father: {fileID: 0}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9005515270017581304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6104174667940741851}
  - component: {fileID: 5007548558710176885}
  m_Layer: 0
  m_Name: "\u63D0\u9192"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6104174667940741851
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9005515270017581304}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3921500894284969093}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5007548558710176885
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9005515270017581304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bd8cc6730391e0d44b62922b568a0cde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  simpleLog: "\u6D4B\u8BD5\u7248\u672C\uFF0C\u524D\u65B9\u533A\u57DF\u672A\u5F00\u653E"
  translate: 1
  localKey: "\u8981\u585E\u63D0\u793A5"
--- !u!1001 &693377650158944324
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3921500894284969093}
    m_Modifications:
    - target: {fileID: 136398, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_Name
      value: pf_vfx-ult_xp-storm_psys_loop_fog_2
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: -721.62
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: -15.1607
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2423.32
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.0000007301568
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.y
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -180
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.94
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.03
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.x
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.y
      value: 7.465999
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.maxNumParticles
      value: 600
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.a
      value: 0.72156864
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0.8207547
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.7971387
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 0.58459413
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.minColor.a
      value: 0.6117647
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.gravityModifier.scalar
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSizeY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSizeZ.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startLifetime.minScalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startRotationX.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startRotationY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19915696, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198603730820833942, guid: bced14ac38c5f404cb4f968623a6b4bb,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 199491915111736854, guid: bced14ac38c5f404cb4f968623a6b4bb,
        type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
--- !u!4 &693377650159074312 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb,
    type: 3}
  m_PrefabInstance: {fileID: 693377650158944324}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3591028807710074295
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3921500894284969093}
    m_Modifications:
    - target: {fileID: 136398, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_Name
      value: pf_vfx-ult_xp-storm_psys_loop_fog
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: -933.16
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: 45.28
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2141.98
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.707107
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.70710665
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.94
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.03
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.x
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.y
      value: 7.465999
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.maxNumParticles
      value: 600
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.a
      value: 0.72156864
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0.8207547
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.7971387
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 0.58459413
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.minColor.a
      value: 0.6117647
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.gravityModifier.scalar
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSizeY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSizeZ.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startLifetime.minScalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startRotationX.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startRotationY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19915696, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198603730820833942, guid: bced14ac38c5f404cb4f968623a6b4bb,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 199491915111736854, guid: bced14ac38c5f404cb4f968623a6b4bb,
        type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
--- !u!4 &3591028807710448123 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb,
    type: 3}
  m_PrefabInstance: {fileID: 3591028807710074295}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6989819189228482581
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 3921500894284969093}
    m_Modifications:
    - target: {fileID: 136398, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_Name
      value: pf_vfx-ult_xp-storm_psys_loop_fog_1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_RootOrder
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: -611.32477
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: 28.04089
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2369.5747
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.0000007301568
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.y
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -180
      objectReference: {fileID: 0}
    - target: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.3
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.94
      objectReference: {fileID: 0}
    - target: {fileID: 452218, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.03
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.x
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.y
      value: 7.465999
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: ShapeModule.m_Scale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.maxNumParticles
      value: 600
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.a
      value: 0.72156864
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0.8207547
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.7971387
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 0.58459413
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startColor.minColor.a
      value: 0.6117647
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.gravityModifier.scalar
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSizeY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startSizeZ.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startLifetime.minScalar
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startRotationX.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19886558, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: InitialModule.startRotationY.minMaxState
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 19915696, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 198603730820833942, guid: bced14ac38c5f404cb4f968623a6b4bb,
        type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 199491915111736854, guid: bced14ac38c5f404cb4f968623a6b4bb,
        type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: bced14ac38c5f404cb4f968623a6b4bb, type: 3}
--- !u!4 &6989819189228358745 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 425036, guid: bced14ac38c5f404cb4f968623a6b4bb,
    type: 3}
  m_PrefabInstance: {fileID: 6989819189228482581}
  m_PrefabAsset: {fileID: 0}
