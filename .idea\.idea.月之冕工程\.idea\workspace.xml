<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cc4a1f63-a407-4f9c-99f5-4cfceccf67ef" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/Assets/Resources" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///AIAssistantSnippet." root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2qxk8CoG41T7qnjM9pYVBI5QlrU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "editor.preferences.fonts.default",
    "vue.rearranger.settings.migration": "true",
    "附加到 Unity 编辑器.附加到 Unity 编辑器.executor": "Debug"
  }
}]]></component>
  <component name="RunManager" selected="附加到 Unity 编辑器.附加到 Unity 编辑器">
    <configuration name="启动 Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="D:\Software\2022.3.0f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath G:\月之冕工程 -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\月之冕工程" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="独立播放器" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="H:/Debug包\moonCorona.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="H:\Debug包" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Standalone Player" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="H:/Debug包\moonCorona.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="H:\Debug包" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="D:\Software\2022.3.0f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath G:\月之冕工程 -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\月之冕工程" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="D:\Software\2022.3.0f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath G:\月之冕工程 -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\月之冕工程" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="单元测试(批处理模式)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="D:\Software\2022.3.0f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath G:\月之冕工程 -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\月之冕工程" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="附加到 Unity 编辑器" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="附加到 Unity 编辑器并运行" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="cc4a1f63-a407-4f9c-99f5-4cfceccf67ef" name="更改" comment="" />
      <created>1735611469331</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735611469331</updated>
      <workItem from="1735611476696" duration="915000" />
      <workItem from="1735613027746" duration="77000" />
      <workItem from="1735613129544" duration="1333000" />
      <workItem from="1735628599502" duration="3398000" />
      <workItem from="1735632289468" duration="6448000" />
      <workItem from="1735786203440" duration="5480000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>