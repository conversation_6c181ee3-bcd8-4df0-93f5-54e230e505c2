using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// PP_Gama 脚本的测试和演示类
/// 用于展示如何使用 gamma 和 gain 控制功能
/// </summary>
public class PP_GamaTest : MonoBehaviour
{
    [Title("Gamma Gain 测试控制")]
    [LabelText("目标 PP_Gama 组件")]
    public PP_Gama targetGamaController;

    [Title("快速预设")]
    [Button("明亮预设")]
    public void ApplyBrightPreset()
    {
        if (targetGamaController != null)
        {
            // 设置明亮的预设：提高 gamma 和 gain
            targetGamaController.SetOverallGamma(1.2f);
            targetGamaController.SetOverallGain(1.1f);
            targetGamaController.SetOverallLift(1.0f);
            targetGamaController.IsActive = true;
            Debug.Log("应用明亮预设");
        }
    }

    [Button("暗沉预设")]
    public void ApplyDarkPreset()
    {
        if (targetGamaController != null)
        {
            // 设置暗沉的预设：降低 gamma 和 gain
            targetGamaController.SetOverallGamma(0.8f);
            targetGamaController.SetOverallGain(0.9f);
            targetGamaController.SetOverallLift(0.95f);
            targetGamaController.IsActive = true;
            Debug.Log("应用暗沉预设");
        }
    }

    [Button("高对比度预设")]
    public void ApplyHighContrastPreset()
    {
        if (targetGamaController != null)
        {
            // 设置高对比度：降低 lift，提高 gain
            targetGamaController.SetOverallLift(0.9f);
            targetGamaController.SetOverallGamma(1.0f);
            targetGamaController.SetOverallGain(1.2f);
            targetGamaController.IsActive = true;
            Debug.Log("应用高对比度预设");
        }
    }

    [Button("电影风格预设")]
    public void ApplyCinematicPreset()
    {
        if (targetGamaController != null)
        {
            // 电影风格：稍微提高 lift，调整 gamma
            targetGamaController.SetLiftGammaGain(
                new Vector4(1.05f, 1.05f, 1.05f, 0.1f), // lift - 稍微提亮阴影
                new Vector4(0.95f, 0.95f, 0.95f, 0f),   // gamma - 稍微降低中间调
                new Vector4(1.0f, 1.0f, 1.0f, 0f)       // gain - 保持高光不变
            );
            targetGamaController.IsActive = true;
            Debug.Log("应用电影风格预设");
        }
    }

    [Button("重置为默认")]
    public void ResetToDefault()
    {
        if (targetGamaController != null)
        {
            targetGamaController.ResetAll();
            Debug.Log("重置为默认值");
        }
    }

    [Title("实时调整测试")]
    [LabelText("整体亮度调整"), PropertyRange(0.5f, 2.0f)]
    public float overallBrightness = 1.0f;

    [LabelText("对比度调整"), PropertyRange(0.5f, 2.0f)]
    public float contrast = 1.0f;

    [Button("应用实时调整")]
    public void ApplyRealtimeAdjustment()
    {
        if (targetGamaController != null)
        {
            // 根据亮度和对比度计算 lift, gamma, gain
            float liftValue = Mathf.Lerp(0.8f, 1.2f, overallBrightness - 0.5f);
            float gammaValue = overallBrightness;
            float gainValue = Mathf.Lerp(0.8f, 1.4f, contrast - 0.5f);

            targetGamaController.SetOverallLift(liftValue);
            targetGamaController.SetOverallGamma(gammaValue);
            targetGamaController.SetOverallGain(gainValue);
            targetGamaController.IsActive = true;

            Debug.Log($"应用实时调整 - 亮度: {overallBrightness}, 对比度: {contrast}");
        }
    }

    [Title("颜色通道独立控制")]
    [LabelText("红色通道增强"), PropertyRange(0.5f, 2.0f)]
    public float redChannelBoost = 1.0f;

    [LabelText("绿色通道增强"), PropertyRange(0.5f, 2.0f)]
    public float greenChannelBoost = 1.0f;

    [LabelText("蓝色通道增强"), PropertyRange(0.5f, 2.0f)]
    public float blueChannelBoost = 1.0f;

    [Button("应用颜色通道调整")]
    public void ApplyColorChannelAdjustment()
    {
        if (targetGamaController != null)
        {
            // 独立调整每个颜色通道的 gamma
            targetGamaController.GammaR = redChannelBoost;
            targetGamaController.GammaG = greenChannelBoost;
            targetGamaController.GammaB = blueChannelBoost;
            targetGamaController.IsActive = true;

            Debug.Log($"应用颜色通道调整 - R: {redChannelBoost}, G: {greenChannelBoost}, B: {blueChannelBoost}");
        }
    }

    [Title("信息显示")]
    [Button("显示当前参数")]
    public void ShowCurrentParameters()
    {
        if (targetGamaController != null)
        {
            var (lift, gamma, gain) = targetGamaController.GetLiftGammaGain();
            Debug.Log($"当前 Lift Gamma Gain 参数:");
            Debug.Log($"Lift: {lift}");
            Debug.Log($"Gamma: {gamma}");
            Debug.Log($"Gain: {gain}");
            Debug.Log($"效果激活状态: {targetGamaController.IsActive}");
        }
    }

    private void Start()
    {
        // 如果没有指定目标，尝试自动查找
        if (targetGamaController == null)
        {
            targetGamaController = FindObjectOfType<PP_Gama>();
            if (targetGamaController != null)
            {
                Debug.Log("自动找到 PP_Gama 组件");
            }
            else
            {
                Debug.LogWarning("未找到 PP_Gama 组件，请手动指定");
            }
        }
    }

    private void Update()
    {
        // 可以在这里添加键盘快捷键控制
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            ApplyBrightPreset();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            ApplyDarkPreset();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            ApplyHighContrastPreset();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            ApplyCinematicPreset();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha0))
        {
            ResetToDefault();
        }
    }
}
