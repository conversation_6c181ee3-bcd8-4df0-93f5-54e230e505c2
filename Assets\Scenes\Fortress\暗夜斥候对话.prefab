%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1784611874329407719
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4210918217664192396}
  - component: {fileID: 5356513784532468245}
  - component: {fileID: 8792782604317724317}
  - component: {fileID: 4103183092513747282}
  m_Layer: 0
  m_Name: "\u6697\u591C\u65A5\u5019\u5BF9\u8BDD"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4210918217664192396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784611874329407719}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0.052}
  m_LocalScale: {x: 0.5, y: 1, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6308353441291972728}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5356513784532468245
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784611874329407719}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8792782604317724317
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784611874329407719}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4103183092513747282
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784611874329407719}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &2192312533840662068
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9054831202872631449}
  - component: {fileID: 4712219441490629860}
  - component: {fileID: 4981257719041315695}
  - component: {fileID: 5720783977458903862}
  - component: {fileID: 411190298220795820}
  - component: {fileID: 6204950213032977422}
  - component: {fileID: 7093053730695671966}
  m_Layer: 0
  m_Name: warning
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9054831202872631449
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_LocalRotation: {x: -8.881809e-16, y: -0.000000014901159, z: -8.8817577e-16, w: 1}
  m_LocalPosition: {x: -0.9025, y: 0.28, z: 1.71}
  m_LocalScale: {x: 14.6191, y: 1.5, z: 5.302218}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1009894344072434470}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4712219441490629860
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4981257719041315695
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5720783977458903862
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &411190298220795820
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 05e8a26c599fd3a4fa5d448ca572667b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  attention: "\u90A3\u4F4D\u670B\u53CB\uFF0C\u8BF7\u7B49\u4E00\u4E0B\uFF01"
  translate: 1
  localKey: "\u8981\u585E\u63D0\u793A3"
--- !u!114 &6204950213032977422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 05e8a26c599fd3a4fa5d448ca572667b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  attention: "\u90A3\u4F4D\u670B\u53CB\uFF0C\u80FD\u6765\u8BF4\u51E0\u53E5\u8BDD\u5417\uFF1F"
  translate: 1
  localKey: "\u8981\u585E\u63D0\u793A4"
--- !u!114 &7093053730695671966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2192312533840662068}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cee5a9299ac601947ad7222dbec22a9d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onlyNormalToTrigger: 1
  enterEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 411190298220795820}
        m_TargetAssemblyTypeName: Temp_Attention, Assembly-CSharp
        m_MethodName: Attention
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  stayEvent:
    m_PersistentCalls:
      m_Calls: []
  exitEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6204950213032977422}
        m_TargetAssemblyTypeName: Temp_Attention, Assembly-CSharp
        m_MethodName: Attention
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!1 &5042488792578971446
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1009894344072434470}
  - component: {fileID: 9165604934867671028}
  - component: {fileID: 100880809633752036}
  - component: {fileID: 4132872631644896784}
  - component: {fileID: 2382106228226063589}
  m_Layer: 0
  m_Name: "\u6697\u591C\u65A5\u5019\u5BF9\u8BDD"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1009894344072434470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5042488792578971446}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -481.1, y: 125.38, z: 2657.1}
  m_LocalScale: {x: 4, y: 4, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6308353441291972728}
  - {fileID: 9054831202872631449}
  m_Father: {fileID: 0}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9165604934867671028
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5042488792578971446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f0ce2f50d3637546a8bf2e8b9399539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  selectedKey: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  m_impactDropDown: "\u6697\u591C\u65A5\u5019\u6D41\u7A0BB"
  selectedOperator: 4
  comparisonValue: 1
  OnVariableCheck:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 5042488792578971446}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!65 &100880809633752036
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5042488792578971446}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 20.846924, y: 14.133938, z: 9.824341}
  m_Center: {x: 0.8683424, y: -3.5125313, z: 1.5283165}
--- !u!114 &4132872631644896784
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5042488792578971446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 10
  useButton: 0
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 2382106228226063589}
--- !u!114 &2382106228226063589
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5042488792578971446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 9165604934867671028}
        m_TargetAssemblyTypeName: VariableCheckUnityEvent, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1 &8673684107441221496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6308353441291972728}
  - component: {fileID: 4211986908606343324}
  - component: {fileID: 9139586949794851761}
  - component: {fileID: 3154844585107351570}
  - component: {fileID: 4944392019418248007}
  - component: {fileID: 5604734261504720285}
  - component: {fileID: 6985333734692263288}
  m_Layer: 0
  m_Name: "\u6697\u591C\u65A5\u5019\u5BF9\u8BDD"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6308353441291972728
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_LocalRotation: {x: -0, y: -0, z: -1.3234925e-23, w: 1}
  m_LocalPosition: {x: -1.31, y: -0, z: 2.58}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4210918217664192396}
  - {fileID: 6643147966404452528}
  m_Father: {fileID: 1009894344072434470}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4211986908606343324
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &9139586949794851761
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3154844585107351570
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.754242, y: 1.6082401, z: 1.7207067}
  m_Center: {x: -0.028912783, y: 0.30412006, z: -0.36035335}
--- !u!114 &4944392019418248007
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 0
  useButton: 1
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base:
  - {fileID: 5604734261504720285}
  - {fileID: 6985333734692263288}
--- !u!114 &5604734261504720285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b343d007a95bbac48a02248358dbf8fc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  dialogueFile: {fileID: 11400000, guid: c84fde03678ef7b42b841d83ca8524dd, type: 2}
--- !u!114 &6985333734692263288
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8673684107441221496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bc9e332e257d724fa3def38123def71, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onInteraction:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 9153402612611199172}
        m_TargetAssemblyTypeName: SpineAnimationPlayer, Assembly-CSharp
        m_MethodName: DoEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 2192312533840662068}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  setNull: 1
--- !u!1 &8957772434070053617
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6643147966404452528}
  - component: {fileID: 1986796124467370513}
  - component: {fileID: 4173628647501857174}
  - component: {fileID: 6971342192798809843}
  - component: {fileID: 9153402612611199172}
  - component: {fileID: 8040261511750044728}
  m_Layer: 0
  m_Name: Spine GameObject (005)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6643147966404452528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8957772434070053617}
  m_LocalRotation: {x: 0.06975647, y: -0, z: -0, w: 0.9975641}
  m_LocalPosition: {x: -0.001, y: -0.464, z: -0.007}
  m_LocalScale: {x: 0.25, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6308353441291972728}
  m_RootOrder: -1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1986796124467370513
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8957772434070053617}
  m_Mesh: {fileID: 0}
--- !u!23 &4173628647501857174
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8957772434070053617}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 351bad6b7009f50469c4a4b3fef7d71f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &6971342192798809843
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8957772434070053617}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d247ba06193faa74d9335f5481b2b56c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonDataAsset: {fileID: 11400000, guid: 791b839bf7caaa04aa7bc6a9a3ed67b9, type: 2}
  initialSkinName: 
  fixPrefabOverrideViaMeshFilter: 2
  initialFlipX: 0
  initialFlipY: 0
  updateWhenInvisible: 3
  separatorSlotNames: []
  zSpacing: -0.0027
  useClipping: 1
  immutableTriangles: 0
  pmaVertexColors: 1
  clearStateOnDisable: 0
  tintBlack: 0
  singleSubmesh: 0
  fixDrawOrder: 0
  addNormals: 0
  calculateTangents: 0
  maskInteraction: 0
  maskMaterials:
    materialsMaskDisabled: []
    materialsInsideMask: []
    materialsOutsideMask: []
  disableRenderingOnOverride: 1
  physicsPositionInheritanceFactor: {x: 1, y: 1}
  physicsRotationInheritanceFactor: 1
  physicsMovementRelativeTo: {fileID: 0}
  updateTiming: 1
  unscaledTime: 0
  _animationName: idle
  loop: 1
  timeScale: 1
--- !u!114 &9153402612611199172
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8957772434070053617}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a05e227012c905a4c9d14b4e496a0dbe, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  skeletonAnimation: {fileID: 6971342192798809843}
  startAnimation: click
  endAnimation: idle
--- !u!114 &8040261511750044728
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8957772434070053617}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d54b59fbefeafb42a72d918521df355, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactionType: 0
  useButton: 1
  useCustomButtonName: 0
  customButtonName: "\u4EA4\u4E92"
  interaction_Base: []
