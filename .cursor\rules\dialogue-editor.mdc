---
description: 
globs: 对话编辑器,SONodeTree,SONode
alwaysApply: false
---
# 对话编辑器系统

该目录包含用于创建、编辑和运行基于 ScriptableObject 的对话树的核心组件。

## 目录结构

- **`Node/`**: 包含所有对话节点的基类和具体实现。
    - `SONode.cs`: ([SONode](mdc:Assets/DialogueEditor/Node/SONode.cs)) 所有节点的抽象基类 (`ScriptableObject`)。定义了基本属性（ID、坐标、子节点）、条件逻辑、全局变量操作和节点生命周期方法 (`开始`, `结束`, `下一个`, `指定节点`)。
    - 具体节点类型 (e.g., `DialogueSONode.cs`, `ChoiseSONode.cs`, `GetItemSONode.cs`): 继承 `SONode` 并实现特定功能，如显示文本、提供选项、给予/消耗物品等。
- **`NodeTree/`**: 定义对话树的数据结构和运行时控制器。
    - `SONodeTree.cs`: ([SONodeTree](mdc:Assets/DialogueEditor/NodeTree/SONodeTree.cs)) 对话树资源 (`ScriptableObject`)。存储所有节点 (`节点列表`)、根节点 (`根节点`) 和运行时状态 (`目前节点`)。提供编辑器接口用于创建/删除/连接节点，以及运行时控制接口 (`开始`, `下一个节点`, `停止`)。
    - `DialogueNodeTreeController.cs`: ([DialogueNodeTreeController](mdc:Assets/DialogueEditor/NodeTree/DialogueNodeTreeController.cs)) `MonoBehaviour` 组件，负责在运行时加载和执行 `SONodeTree`。通过 `对话逻辑判断` 方法根据节点类型调用 `DialogueManager` 来驱动对话流程。
- **`Editor/`**: 包含 Unity 编辑器扩展，用于可视化编辑对话树。
    - `NodeTreeViewer.cs`: ([NodeTreeViewer](mdc:Assets/DialogueEditor/Editor/NodeTreeViewer.cs)) 基于 `GraphView` 的编辑器窗口，用于显示和编辑 `SONodeTree`。处理节点的创建、删除、连接，并提供右键菜单。
    - `NodeView.cs`: ([NodeView](mdc:Assets/DialogueEditor/Editor/NodeView.cs)) 编辑器中 `SONode` 的可视化表示，包含输入/输出端口。
    - `InspectorViewer.cs`: ([InspectorViewer](mdc:Assets/DialogueEditor/Editor/InspectorViewer.cs)) 可能用于在 Inspector 中显示选定节点的详细信息。
    - `.uxml`, `.uss` 文件: 定义编辑器的 UI 结构和样式。