%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9056153023324527970
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 294312c99f3b0a8428fd7aa0871d9d4b
  "\u8282\u70B9\u5750\u6807": {x: 618.99994, y: 735.1032}
  "\u5B50\u8282\u70B9":
  - {fileID: -6910022163988203057}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6216\u8BB8\u4F60\u5E76\u6CA1\u6709\u60F3\u8FC7\u6210\u4E3A\u4E00\u540D\u9A91\u58EB\uFF0C\u4F46\u5728\u6211\u5FC3\u91CC\uFF0C\u4F60\u5DF2\u7ECF\u5177\u5907\u4E86\u4E00\u4E2A\u4F18\u79C0\u9A91\u58EB\u7684\u5168\u90E8\u6761\u4EF6\u3002\u52C7\u6562\uFF0C\u575A\u5B9A\uFF0C\u9AD8\u5C1A\u7684\u54C1\u683C\uFF0C\u65E0\u754F\u7684\u7CBE\u795E\u2026\u2026"
--- !u!114 &-8537579485738583168
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4e60b5de9dbab7b4da8c4f9c9a470dbe
  "\u8282\u70B9\u5750\u6807": {x: -82.00017, y: 1330.316}
  "\u5B50\u8282\u70B9":
  - {fileID: 5261183843725718922}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u771F\u4F69\u670D\u4F60\u7684\u52C7\u6C14\uFF0C\u4F46\u6211\u8FD8\u662F\u60F3\u529D\u4F60\u4E0D\u8981\u53BB\u5192\u9669\u2026\u2026\u6211\u4E0D\u80FD\u773C\u7741\u7741\u770B\u7740\u91CD\u8981\u7684\u670B\u53CB\u767D\u767D\u9001\u547D\u3002"
--- !u!114 &-6910022163988203057
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": fe22563b45c993f4188ce1c84160111d
  "\u8282\u70B9\u5750\u6807": {x: 653.9999, y: 970.00006}
  "\u5B50\u8282\u70B9":
  - {fileID: -1479452807642609879}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\uFF08\u57C3\u5FB7\u8499\u4F4E\u5934\u5728\u8EAB\u4E0A\u7FFB\u627E\u7740\u4EC0\u4E48\uFF09\u5514\u2026\u2026\u2026\u2026"
--- !u!114 &-6357601559522468097
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
    - Name: $v
      Entry: 3
      Data: 3
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 263efe491884ac0459cfc0b4f96ce237
  "\u8282\u70B9\u5750\u6807": {x: 703.99994, y: 2462.4055}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E0D\u8FC7\u4F60\u7B49\u7740\u5427\uFF0C\u4E0D\u7528\u591A\u4E45\u6211\u5C31\u4F1A\u6210\u4E3A\u80FD\u548C\u4F60\u5E76\u80A9\u7684\u4F19\u4F34\uFF01"
--- !u!114 &-6270089136283395927
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u524D\u5F80\u5229\u5965\u62C9\u65AF"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 8207d1de25e448f488b7af08b291a9ee
  "\u8282\u70B9\u5750\u6807": {x: -94.99986, y: 962}
  "\u5B50\u8282\u70B9":
  - {fileID: 2655948762923031867}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F60\u8981\u53BB\u5229\u5965\u62C9\u65AF\uFF1F\u524D\u5F80\u90A3\u53EA\u6709\u4E00\u6761\u8DEF\uFF0C\u5C31\u662F\u7A7F\u8FC7\u90A3\u9053\u201C\u65E0\u6CD5\u7A81\u7834\u7684\u5821\u5792\u201D\uFF0C\u5FB7\u58A8\u94C1\u7FFC\u8981\u585E\u2026\u2026"
--- !u!114 &-4193332683593626514
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f716134a78f37df418d8eb84a3f5229d, type: 3}
  m_Name: ConditionCheckSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": df8458d5e7c3c8a4ba5fac6e06ef84a4
  "\u8282\u70B9\u5750\u6807": {x: 626.501, y: -132.54347}
  "\u5B50\u8282\u70B9":
  - {fileID: 8458913621828508510}
  - {fileID: 1525807503943774674}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &-3995910526429751711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 26e145f5705dd6d4780d1661aa1e6d84
  "\u8282\u70B9\u5750\u6807": {x: -102.39998, y: 388.00003}
  "\u5B50\u8282\u70B9":
  - {fileID: 2831089935607834611}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u542C\u8BF4\u4F60\u53BB\u4E86\u68EE\u6797\uFF0C\u8FD8\u6253\u8D25\u4E86\u68EE\u6797\u5C3D\u5934\u90A3\u7247\u8346\u68D8\u4E4B\u5730\u4E2D\u5FC3\u7684\u5DE8\u5927\u53E4\u6728\uFF0C\u4F60\u771F\u5389\u5BB3\uFF01\u771F\u6B63\u7684\u9A91\u58EB\u5E94\u8BE5\u5C31\u50CF\u4F60\u4E00\u6837\u5427\u2026\u2026\u5F3A\u5927\uFF0C\u52C7\u6562\uFF0C\u503C\u5F97\u4FE1\u8D56\u3002"
--- !u!114 &-3792282250232923802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 455d0358f7e91c14895b71ebe4dbdbd1
  "\u8282\u70B9\u5750\u6807": {x: -88.9999, y: 1905.5}
  "\u5B50\u8282\u70B9":
  - {fileID: 8761501388236884444}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u53EF\u4E0D\u662F\u5728\u5413\u552C\u4F60\uFF0C\u60C5\u51B5\u5C31\u662F\u8FD9\u4E48\u7CDF\u7CD5\uFF01\u6211\u660E\u767D\uFF0C\u8EAB\u4E3A\u9A91\u58EB\u5E94\u8BE5\u65E0\u60E7\u5371\u9669\uFF0C\u4F46\u662F\u2026\u2026\u6211\u771F\u5BB3\u6015\u770B\u5230\u4F60\u5931\u53BB\u80F3\u818A\u6216\u662F\u53CC\u817F\u7684\u6837\u5B50\u2026\u2026\u4F60\u4E00\u5B9A\u8981\u53BB\u5417\uFF1F"
--- !u!114 &-1479452807642609879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": b590aa1411528e8408fdca8dd2f91f4b
  "\u8282\u70B9\u5750\u6807": {x: 695.99994, y: 1185.0914}
  "\u5B50\u8282\u70B9":
  - {fileID: 9006602349743426495}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8FD9\u4E2A\u2026\u2026\u8BF7\u6536\u4E0B\u3002"
--- !u!114 &-598624266903002824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 32c338fc61fc82340ad9ed708be3e49a
  "\u8282\u70B9\u5750\u6807": {x: -89.99999, y: 763.78015}
  "\u5B50\u8282\u70B9":
  - {fileID: -6270089136283395927}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-240887481875995336
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": bb5f077b95da1ae438fa099fb29a9b9e
  "\u8282\u70B9\u5750\u6807": {x: 662, y: 1605.8405}
  "\u5B50\u8282\u70B9":
  - {fileID: 312933231792239812}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u522B\u5C0F\u77A7\u4E86\u8FD9\u4EFD\u7B14\u8BB0\uFF0C\u8FD9\u662F\u6211\u7684\u79D8\u5BC6\u6B66\u5668\u2026\u2026\u53EA\u80FD\u4EA4\u7ED9\u91CD\u8981\u7684\uFF0C\u503C\u5F97\u4FE1\u4EFB\u7684\u670B\u53CB\u3002"
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 204e534958e91824783fa0a7dc10275e, type: 3}
  m_Name: "\u57C3\u5FB7\u8499\u6708\u89C1\u6751\u5BF9\u8BDD3"
  m_EditorClassIdentifier: 
  treeGuid: 4a3bab3fd6efce94aa64cbd2eafc3a3e
  "\u6839\u8282\u70B9": {fileID: -4193332683593626514}
  "\u76EE\u524D\u8282\u70B9": {fileID: -4193332683593626514}
  "\u6811\u72B6\u6001": 1
  "\u8282\u70B9\u5217\u8868":
  - {fileID: -4193332683593626514}
  - {fileID: 8458913621828508510}
  - {fileID: -3995910526429751711}
  - {fileID: 2831089935607834611}
  - {fileID: -598624266903002824}
  - {fileID: -6270089136283395927}
  - {fileID: 2655948762923031867}
  - {fileID: -8537579485738583168}
  - {fileID: 5261183843725718922}
  - {fileID: 9186954543760764860}
  - {fileID: -3792282250232923802}
  - {fileID: 8761501388236884444}
  - {fileID: -9056153023324527970}
  - {fileID: -1479452807642609879}
  - {fileID: 9006602349743426495}
  - {fileID: -240887481875995336}
  - {fileID: 312933231792239812}
  - {fileID: 1492600991238576688}
  - {fileID: 637300327903127336}
  - {fileID: -6357601559522468097}
  - {fileID: 1525807503943774674}
  - {fileID: -6910022163988203057}
--- !u!114 &312933231792239812
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 242586de8e37eb1478575a5a8494e506
  "\u8282\u70B9\u5750\u6807": {x: 670, y: 1814.5815}
  "\u5B50\u8282\u70B9":
  - {fileID: 1492600991238576688}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u66FE\u7ECF\u8DDF\u968F\u9A91\u58EB\u56E2\u53BB\u8FC7\u5FB7\u58A8\u8981\u585E\uFF0C\u5BF9\u91CC\u9762\u7684\u73AF\u5883\u6709\u4E00\u4E9B\u5370\u8C61\u2026\u2026\u8FD9\u662F\u6211\u6839\u636E\u8BB0\u5FC6\u5199\u4E0B\u7684\u6CE8\u610F\u4E8B\u9879\uFF0C\u6216\u8BB8\u80FD\u5E2E\u4E0A\u4F60\u7684\u5FD9\u3002"
--- !u!114 &637300327903127336
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 1debdcf529931e34d829835cd7dcce94
  "\u8282\u70B9\u5750\u6807": {x: 703.99994, y: 2255.8086}
  "\u5B50\u8282\u70B9":
  - {fileID: -6357601559522468097}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u5176\u5B9E\u6211\u771F\u7684\u5F88\u60F3\u8DDF\u4F60\u4E00\u8D77\u53BB\uFF01\u4F46\u6211\u77E5\u9053\u4EE5\u6211\u73B0\u5728\u7684\u80FD\u529B\uFF0C\u8DDF\u7740\u4F60\u4E5F\u53EA\u4F1A\u5E26\u6765\u9EBB\u70E6\u2026\u2026"
--- !u!114 &1492600991238576688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 34f19e96fadccf740b96b8788adf4381
  "\u8282\u70B9\u5750\u6807": {x: 736.0159, y: 2032.2484}
  "\u5B50\u8282\u70B9":
  - {fileID: 637300327903127336}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u2026\u2026"
--- !u!114 &1525807503943774674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 43e2821d4f8ede54d97cce4ac9b46eca
  "\u8282\u70B9\u5750\u6807": {x: 1228.2882, y: 306.64215}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 3
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u4E5F\u5FC5\u987B\u53D8\u5F97\u66F4\u5F3A\u554A\uFF0C\u4E3A\u4E86\u4E0B\u6B21\u80FD\u548C\u4F60\u540C\u884C\u2026\u2026"
--- !u!114 &2655948762923031867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": c3b345423a6428746b604b2c58cd31ba
  "\u8282\u70B9\u5750\u6807": {x: -113.10003, y: 1142.9}
  "\u5B50\u8282\u70B9":
  - {fileID: -8537579485738583168}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u662F\u4E2A\u5F88\u5192\u9669\u7684\u51B3\u5B9A\u5462\uFF0C\u4F46\u6211\u5E76\u4E0D\u610F\u5916\uFF0C\u4F3C\u4E4E\u4ECE\u4E00\u5F00\u59CB\u5C31\u6709\u79CD\u4F60\u4F1A\u8FD9\u4E48\u505A\u7684\u76F4\u89C9\u2026\u2026\u6211\u7684\u76F4\u89C9\u5F88\u51C6\u7684\uFF0C\u548C\u59D0\u59D0\u4E00\u6837\u3002"
--- !u!114 &2831089935607834611
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 96029f0099bda2448a627d7fac9e3560
  "\u8282\u70B9\u5750\u6807": {x: -89.99999, y: 575.31805}
  "\u5B50\u8282\u70B9":
  - {fileID: -598624266903002824}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u80FD\u544A\u8BC9\u6211\u4F60\u63A5\u4E0B\u6765\u6709\u4EC0\u4E48\u6253\u7B97\u5417\uFF1F"
--- !u!114 &5261183843725718922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 07fce8b51299909469742d4c6012dc59
  "\u8282\u70B9\u5750\u6807": {x: -104.99986, y: 1518.0504}
  "\u5B50\u8282\u70B9":
  - {fileID: 9186954543760764860}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5B9E\u9645\u4E0A\uFF0C\u5728\u4F60\u4E4B\u524D\u4E5F\u6709\u4E0D\u5C11\u4EBA\u60F3\u8981\u901A\u8FC7\u8981\u585E\u524D\u5F80\u5229\u5965\u62C9\u65AF\uFF0C\u4ED6\u4EEC\u4E2D\u6709\u7684\u662F\u8EAB\u7ECF\u767E\u6218\u7684\u58EB\u5175\uFF0C\u6709\u4E9B\u662F\u7ECF\u9A8C\u4E30\u5BCC\u7684\u5192\u9669\u8005\uFF0C\u751A\u81F3\u8FD8\u6709\u6325\u821E\u7740\u6CD5\u6756\u7684\u9B54\u6CD5\u5E08\u2026\u2026"
--- !u!114 &8458913621828508510
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 86a1dfbc6266cc14386e0366ecfde935
  "\u8282\u70B9\u5750\u6807": {x: -15.476868, y: 190.62425}
  "\u5B50\u8282\u70B9":
  - {fileID: -3995910526429751711}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u6708\u89C1\u6751\u57C3\u5FB7\u8499\u6D41\u7A0B"
      "\u8FD0\u7B97\u7B26": 4
      "\u503C": 2
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F60\u56DE\u6765\u4E86\uFF0C\u771F\u5F00\u5FC3\u80FD\u518D\u89C1\u5230\u4F60\u3002"
--- !u!114 &8761501388236884444
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u6211\u5DF2\u7ECF\u51B3\u5B9A\u4E86"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": da73af7f1407efd4180b2b2c839a8dfa
  "\u8282\u70B9\u5750\u6807": {x: 627.00006, y: 484.99997}
  "\u5B50\u8282\u70B9":
  - {fileID: -9056153023324527970}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u770B\u6837\u5B50\u4F60\u5DF2\u7ECF\u505A\u597D\u4E86\u51C6\u5907\u3002"
--- !u!114 &9006602349743426495
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b8e85c9ec5527c4596a9d27a9bc04c6, type: 3}
  m_Name: GetItemSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u83B7\u53D6\u7269\u54C1\u5217\u8868"
      Entry: 7
      Data: 3|System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 4|System.Collections.Generic.GenericEqualityComparer`1[[System.Int32,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 50014
    - Name: $v
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 6451fa0bb81b73947a10c32651d3453a
  "\u8282\u70B9\u5750\u6807": {x: 695.99994, y: 1395.1073}
  "\u5B50\u8282\u70B9":
  - {fileID: -240887481875995336}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &9186954543760764860
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": a883df6cefb378f428d8a632fc47b428
  "\u8282\u70B9\u5750\u6807": {x: -97, y: 1704}
  "\u5B50\u8282\u70B9":
  - {fileID: -3792282250232923802}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f1c6916f18306bc4eabecbb59828a459, type: 3}
  "\u540D\u5B57": "\u57C3\u5FB7\u8499"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u53EF\u662F\u5F88\u5C11\u6709\u4EBA\u6210\u529F\u2026\u2026\u5E78\u8FD0\u7684\uFF0C\u8EAB\u53D7\u91CD\u4F24\u9003\u4E86\u56DE\u6765\uFF0C\u4E0D\u5E78\u7684\u2026\u2026"
