%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8741976054820377735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 681f15c49bb01cd4aba9cc38e73c0625
  "\u8282\u70B9\u5750\u6807": {x: -355.34137, y: 972.76807}
  "\u5B50\u8282\u70B9":
  - {fileID: 3607411013725268958}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5229\u5965\u62C9\u65AF\u4F4D\u4E8E\u516C\u56FD\u7684\u5FC3\u810F\u5730\u5E26\uFF0C\u662F\u6700\u7E41\u8363\u7684\u5730\u65B9\uFF0C\u90A3\u91CC\u6709\u6700\u5927\u7684\u9ED1\u591C\u5973\u795E\u6559\u5802\uFF0C\u6709\u516C\u7235\u4E00\u624B\u521B\u529E\u7684\u5B66\u9662\uFF0C\u5C31\u8FDE\u5927\u96C6\u5E02\u90FD\u6709\u4E94\u516D\u4E2A\u5462\uFF01"
--- !u!114 &-7871162242256001511
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f60862ed8b11cbe4bb60fa39e2f75a74
  "\u8282\u70B9\u5750\u6807": {x: -376.34128, y: 355.76813}
  "\u5B50\u8282\u70B9":
  - {fileID: -5371487559203434411}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-7777379913657675472
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 0ed35a36c418660488d48059c37454f9
  "\u8282\u70B9\u5750\u6807": {x: -352.3412, y: 748.76807}
  "\u5B50\u8282\u70B9":
  - {fileID: -8741976054820377735}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4ED6\u4EEC\u8BF4\u201C\u5229\u5965\u62C9\u65AF\u201D\u662F\u96C4\u72EE\u4E4B\u4EBA\u6C11\u7684\u610F\u601D\uFF0C\u542C\u8BF4\u5EFA\u7ACB\u4E86\u83B1\u6069\u54C8\u7279\u516C\u56FD\u7684\u6469\u6839\u5BB6\u65CF\uFF0C\u5BB6\u5FBD\u5C31\u662F\u4E00\u53EA\u96C4\u72EE\u3002"
--- !u!114 &-5371487559203434411
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5173\u4E8E\u5229\u5965\u62C9\u65AF"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 05fa2750c541d024790e69f6c22e4163
  "\u8282\u70B9\u5750\u6807": {x: -368.34125, y: 533.76807}
  "\u5B50\u8282\u70B9":
  - {fileID: -7777379913657675472}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u563F\u563F\u2026\u2026\u6211\u6BD4\u4E0D\u4E0A\u4F60\u4EEC\u62A4\u536B\u961F\u54EA\u91CC\u90FD\u53BB\u8FC7\uFF0C\u50CF\u5229\u5965\u62C9\u65AF\u8FD9\u79CD\u5927\u57CE\u5E02\uFF0C\u6211\u53EA\u542C\u5546\u961F\u524D\u8F88\u8BF4\u8FC7\u800C\u5DF2\u2026\u2026"
--- !u!114 &-5337707576515675446
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4e7bc28ee9cb9794c960b13a47a29313
  "\u8282\u70B9\u5750\u6807": {x: -75, y: 133}
  "\u5B50\u8282\u70B9":
  - {fileID: -7871162242256001511}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u662F\u5426\u548C\u5546\u961F\u6210\u54581\u5BF9\u8BDD"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 0
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u563F\uFF0C\u6253\u8D77\u7CBE\u795E\u6765\uFF0C\u524D\u9762\u5C31\u5FEB\u5230\u5229\u5965\u62C9\u65AF\u4E86\uFF01\u827E\u4F2F\u7279\u5148\u751F\u8BF4\u54B1\u4EEC\u4ECA\u665A\u5C31\u5728\u8FD9\u4F11\u606F\uFF0C\u660E\u5929\u4E00\u65E9\u51FA\u53D1\u3002"
--- !u!114 &-4853709486019788013
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": d5ab50e56bcd56b42bfdb537d0f708c8
  "\u8282\u70B9\u5750\u6807": {x: -381.34134, y: 1660.7682}
  "\u5B50\u8282\u70B9":
  - {fileID: 8205496104531622063}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u4EEC\u9A6C\u4E0A\u5C31\u8981\u53BB\u5230\u90A3\u6837\u7684\u5927\u57CE\u5E02\u4E86\uFF01\u5149\u662F\u60F3\u60F3\uFF0C\u6211\u5C31\u6FC0\u52A8\u5F97\u6068\u4E0D\u5F97\u7ACB\u523B\u51FA\u53D1\uFF0C\u8D76\u7D27\u53BB\u90A3\u91CC\u627E\u4E00\u5BB6\u6700\u5927\u7684\u9152\u9986\u2026\u2026\u8FD9\u4E00\u8DEF\u6211\u53EF\u53D7\u591F\u4F0A\u68EE\u7684\u6599\u7406\u4E86\u2026\u2026"
--- !u!114 &-817336096908340141
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 0ff9b63422fd8974994e121a15f4123f
  "\u8282\u70B9\u5750\u6807": {x: -355.34137, y: 1452.7681}
  "\u5B50\u8282\u70B9":
  - {fileID: -4853709486019788013}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u827E\u4F2F\u7279\u5148\u751F\u544A\u8BC9\u8FC7\u6211\uFF0C\u5229\u5965\u62C9\u65AF\u5373\u4FBF\u5230\u4E86\u6DF1\u591C\u8DEF\u4E0A\u7684\u706F\u5149\u4E5F\u4E0D\u4F1A\u7184\u706D\uFF0C\u5230\u5904\u90FD\u662F\u9152\u9986\u548C\u5192\u9669\u8005\u5DE5\u4F1A\uFF0C\u50CF\u6211\u4EEC\u8FD9\u6837\u7684\u5546\u961F\u6BCF\u5929\u4E0D\u77E5\u9053\u6709\u591A\u5C11\u2026\u2026"
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 204e534958e91824783fa0a7dc10275e, type: 3}
  m_Name: "\u5546\u961F\u6210\u54581\u5BF9\u8BDD"
  m_EditorClassIdentifier: 
  treeGuid: a037137eef395e142bc1080d27a962d8
  "\u6839\u8282\u70B9": {fileID: 3287045233681800162}
  "\u76EE\u524D\u8282\u70B9": {fileID: 0}
  "\u6811\u72B6\u6001": 1
  "\u8282\u70B9\u5217\u8868":
  - {fileID: 3287045233681800162}
  - {fileID: 2204133373612631183}
  - {fileID: -5337707576515675446}
  - {fileID: 676860875869133276}
  - {fileID: -7871162242256001511}
  - {fileID: -5371487559203434411}
  - {fileID: 8205496104531622063}
  - {fileID: -4853709486019788013}
  - {fileID: -817336096908340141}
  - {fileID: -8741976054820377735}
  - {fileID: 3607411013725268958}
  - {fileID: -7777379913657675472}
--- !u!114 &676860875869133276
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": a2f6df91604cd9745bb75228a415b5af
  "\u8282\u70B9\u5750\u6807": {x: 722.6816, y: 376}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u8FD8\u6709\u6211\u521A\u521A\u7684\u8BDD\uFF0C\u53EF\u5343\u4E07\u522B\u544A\u8BC9\u4F0A\u68EE\u554A\u2026\u2026"
--- !u!114 &2204133373612631183
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4faee6df38613f74688054d07639fafe
  "\u8282\u70B9\u5750\u6807": {x: 693.6815, y: 141.00005}
  "\u5B50\u8282\u70B9":
  - {fileID: 676860875869133276}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u662F\u5426\u548C\u5546\u961F\u6210\u54581\u5BF9\u8BDD"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 1
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u5B89\u8FEA\u548C\u4E54\u6CBB\u53BB\u9644\u8FD1\u627E\u98DF\u7269\u4E86\uFF0C\u79BB\u665A\u996D\u8FD8\u6709\u6BB5\u65F6\u95F4\uFF0C\u4F60\u8981\u4E0D\u8981\u4E5F\u56DB\u5904\u770B\u770B\uFF1F"
--- !u!114 &3287045233681800162
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f716134a78f37df418d8eb84a3f5229d, type: 3}
  m_Name: ConditionCheckSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f3e6ee07fc10b2f4bad16b3571af9a62
  "\u8282\u70B9\u5750\u6807": {x: 75, y: -109.51993}
  "\u5B50\u8282\u70B9":
  - {fileID: -5337707576515675446}
  - {fileID: 2204133373612631183}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &3607411013725268958
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 227f5ce0e252b644588c1ccac6d639b4
  "\u8282\u70B9\u5750\u6807": {x: -363.34122, y: 1219.7681}
  "\u5B50\u8282\u70B9":
  - {fileID: -817336096908340141}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F60\u8FD8\u8BB0\u5F97\u4E4B\u524D\u7ECF\u8FC7\u79CB\u8C37\u9547\u65F6\u6211\u4EEC\u78B0\u4E0A\u7684\u90A3\u4E2A\u96C6\u5E02\u5417\uFF1F\u6211\u5DF2\u7ECF\u89C9\u5F97\u89C4\u6A21\u5F88\u58EE\u89C2\u4E86\uFF0C\u53EF\u50CF\u90A3\u6837\u7684\u96C6\u5E02\u5728\u5229\u5965\u62C9\u65AF\u6839\u672C\u4E0D\u7B97\u4EC0\u4E48\uFF01"
--- !u!114 &8205496104531622063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u662F\u5426\u548C\u5546\u961F\u6210\u54581\u5BF9\u8BDD"
    - Name: $v
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 7bdec5c27d130db4ba42a1f9bd93548a
  "\u8282\u70B9\u5750\u6807": {x: -373.3413, y: 1882.7682}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: f0f4a900c17694f42a61e9c73783a529, type: 3}
  "\u540D\u5B57": "\u683C\u745E\u59C6"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u2026\u2026\u54B3\u54B3\uFF0C\u6211\u662F\u8BF4\uFF0C\u867D\u7136\u4ED6\u70ED\u7231\u6599\u7406\uFF0C\u4F46\u4ED6\u6BD5\u7ADF\u8F9B\u82E6\u4E00\u8DEF\u4E86\uFF0C\u4E5F\u8BE5\u4F11\u606F\u4E00\u4E0B\u4E86\u5BF9\u5427\u2026\u2026"
