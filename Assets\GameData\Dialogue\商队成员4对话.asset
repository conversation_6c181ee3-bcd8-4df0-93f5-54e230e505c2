%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8606507978622326014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 2cd0a4b03b705324c932b7fae9760018
  "\u8282\u70B9\u5750\u6807": {x: -218.99973, y: 316.13913}
  "\u5B50\u8282\u70B9":
  - {fileID: -5649794811696425675}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u522B\u5728\u610F\uFF0C\u4ED6\u4E00\u76F4\u90FD\u662F\u90A3\u6837\uFF0C\u6574\u5929\u561F\u54DD\u7740\u201C\u65F6\u95F4\u5C31\u662F\u91D1\u94B1\u201D\u4EC0\u4E48\u7684\u2026\u2026\u8981\u771F\u662F\u8FD9\u6837\uFF0C\u90A3\u6211\u5E94\u8BE5\u7B97\u5F97\u4E0A\u5168\u516C\u56FD\u6700\u5BCC\u6709\u7684\u4EBA\u4E86\uFF0C\u5475\u5475\u2026\u2026"
--- !u!114 &-7782302997227550536
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4e4c1cded5acc0e468fb6c8107a93d4b
  "\u8282\u70B9\u5750\u6807": {x: 397.344, y: 535}
  "\u5B50\u8282\u70B9":
  - {fileID: -2558077689378343136}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4ED6\u6700\u8FD1\u603B\u662F\u604D\u604D\u60DA\u60DA\u7684\uFF0C\u6211\u771F\u62C5\u5FC3\u4ED6\u8D70\u8DEF\u90FD\u4F1A\u6389\u8FDB\u5751\u91CC\u3002"
--- !u!114 &-6743563592003851563
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u662F\u5426\u548C\u5546\u961F\u6210\u54584\u5BF9\u8BDD"
    - Name: $v
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": c37ac7b235af6fa45a357871cec6b1e7
  "\u8282\u70B9\u5750\u6807": {x: -167.30011, y: 1724.8503}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 1
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4E0D\u8FC7\u4ED6\u4EEC\u600E\u4E48\u8FD8\u6CA1\u56DE\uFF1F\u518D\u4E0D\u5F00\u996D\uFF0C\u4E00\u4F1A\u827E\u4F2F\u7279\u5148\u751F\u53C8\u8981\u5520\u53E8\u6211\u4E86\u2026\u2026"
--- !u!114 &-5649794811696425675
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": d863cfb335a423045bcd4dfcb099f7fe
  "\u8282\u70B9\u5750\u6807": {x: -210.99973, y: 526.97614}
  "\u5B50\u8282\u70B9":
  - {fileID: -7782302997227550536}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6BD4\u8D77\u90A3\u4E9B\uFF0C\u5FEB\u95FB\u95FB\u8FD9\u4E2A\uFF0C\u6211\u7684\u81EA\u4FE1\u4E4B\u4F5C\uFF01\u5473\u9053\u5F88\u4E0D\u9519\u5427\uFF1F\u8981\u662F\u518D\u5F04\u4E9B\u6D46\u679C\u548C\u8568\u83DC\u8425\u517B\u5C31\u9F50\u5168\u4E86\uFF01\u4F46\u613F\u5B89\u8FEA\u522B\u5FD8\u8BB0\u2026\u2026"
--- !u!114 &-3322768956708081494
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": "\u5173\u4E8E\u5B89\u8FEA"
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 09359f0b638eb9e409e6743ad9c9e5d7
  "\u8282\u70B9\u5750\u6807": {x: -242, y: 929}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4F60\u4E5F\u6CE8\u610F\u5230\u4E86\u662F\u5417\uFF1F\u4ED6\u6700\u8FD1\u5FC3\u60C5\u4E0D\u592A\u597D\uFF0C\u5C24\u5176\u8FD9\u51E0\u5929\uFF0C\u603B\u662F\u5F88\u66B4\u8E81\u2026\u2026\u6709\u5929\u665A\u4E0A\u6211\u8FD8\u542C\u5230\u4ED6\u5F88\u6025\u5207\u5730\u8981\u6C42\u827E\u4F2F\u7279\u5148\u751F\u8D76\u5FEB\u51FA\u53D1\uFF0C\u5C3D\u5FEB\u8D76\u5230\u5229\u5965\u62C9\u65AF\u2026\u2026"
--- !u!114 &-2558077689378343136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47a4b21a27a6ae946a8aa82627a1ac0e, type: 3}
  m_Name: ChoiseSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": dc97864a25ccf8948afacba12263f091
  "\u8282\u70B9\u5750\u6807": {x: -155.00027, y: 728.0517}
  "\u5B50\u8282\u70B9":
  - {fileID: -3322768956708081494}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u9690\u85CF\u5BF9\u8BDD\u7A97\u53E3": 0
--- !u!114 &-2532644926316163608
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 4fbfdd8e94733544b9b451c0ee590704
  "\u8282\u70B9\u5750\u6807": {x: 464.00006, y: 153.99997}
  "\u5B50\u8282\u70B9": []
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u56E2\u957F\uFF0C\u4F60\u80AF\u5B9A\u4E5F\u4E0D\u60F3\u88AB\u6B83\u53CA\u5427\uFF1F\u8981\u4E0D\u4F60\u53BB\u627E\u627E\u4ED6\u4EEC\uFF1F\u6211\u8BB0\u5F97\u4ED6\u4EEC\u53BB\u4E86\u897F\u8FB9\u2026\u2026"
--- !u!114 &-1165202698175242227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": a0d1fadeb0df99b4eaeff1f3ef643d63
  "\u8282\u70B9\u5750\u6807": {x: -147.00008, y: 1335}
  "\u5B50\u8282\u70B9":
  - {fileID: 2233411235036287844}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u542C\u8BF4\u5B89\u8FEA\u7684\u672A\u5A5A\u59BB\u5728\u4E00\u4E2A\u591A\u6708\u524D\u53BB\u4E86\u5229\u5965\u62C9\u65AF\u62DC\u8BBF\u4EB2\u621A\uFF0C\u4ED6\u4EEC\u7EA6\u597D\u4E86\u5728\u90A3\u6C47\u5408\u3002\u4E0A\u5468\u4ED6\u6536\u5230\u4E86\u672A\u5A5A\u59BB\u5B89\u5168\u62B5\u8FBE\u7684\u6D88\u606F\uFF0C\u53EF\u6700\u8FD1\uFF0C\u4ED6\u5BC4\u51FA\u7684\u4FE1\u4EF6\u5374\u7A81\u7136\u6CA1\u4E86\u56DE\u590D\u2026\u2026"
--- !u!114 &-562688680005327558
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 9868b4efe123593469fa9a6d86800065
  "\u8282\u70B9\u5750\u6807": {x: -226.99997, y: 117}
  "\u5B50\u8282\u70B9":
  - {fileID: -8606507978622326014}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u54DF\uFF0C\u56E2\u957F\u4F60\u6765\u4E86\uFF01\u600E\u4E48\u82E6\u7740\u8138\uFF0C\u53C8\u88AB\u6211\u4EEC\u827E\u4F2F\u7279\u5148\u751F\u8BAD\u65A5\u4E86\uFF1F"
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 204e534958e91824783fa0a7dc10275e, type: 3}
  m_Name: "\u5546\u961F\u6210\u54584\u5BF9\u8BDD"
  m_EditorClassIdentifier: 
  treeGuid: ceec77caab7cbc6468c7e0cef18d7cd9
  "\u6839\u8282\u70B9": {fileID: 3205896313162100493}
  "\u76EE\u524D\u8282\u70B9": {fileID: 0}
  "\u6811\u72B6\u6001": 1
  "\u8282\u70B9\u5217\u8868":
  - {fileID: 3205896313162100493}
  - {fileID: 2804478208270175289}
  - {fileID: -5649794811696425675}
  - {fileID: 9156018364789118897}
  - {fileID: -2532644926316163608}
  - {fileID: -2558077689378343136}
  - {fileID: -3322768956708081494}
  - {fileID: -6743563592003851563}
  - {fileID: -562688680005327558}
  - {fileID: -8606507978622326014}
  - {fileID: 2233411235036287844}
  - {fileID: -7782302997227550536}
  - {fileID: -1165202698175242227}
--- !u!114 &2233411235036287844
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": e4f7cf8732614a745a1680d6d9bb1d4a
  "\u8282\u70B9\u5750\u6807": {x: -162.30008, y: 1526.8503}
  "\u5B50\u8282\u70B9":
  - {fileID: -6743563592003851563}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u6211\u529D\u8FC7\u4ED6\u522B\u592A\u62C5\u5FC3\u2026\u2026\u6BD5\u7ADF\uFF0C\u90A3\u53EF\u662F\u5229\u5965\u62C9\u65AF\u554A\uFF01\u6574\u4E2A\u516C\u56FD\u6CA1\u6709\u6BD4\u90A3\u66F4\u5B89\u5168\u7684\u5730\u65B9\u4E86\u2026\u2026\u5982\u679C\u6211\u5230\u4E86\u90A3\u91CC\uFF0C\u4E00\u5B9A\u4E5F\u4F1A\u5148\u75AF\u73A9\u51E0\u5929\u7684\uFF0C\u54EA\u91CC\u8FD8\u987E\u5F97\u4E0A\u56DE\u4FE1\u2026\u2026"
--- !u!114 &2804478208270175289
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": 7999f7bb6c6ecd24e9fb634af4529d08
  "\u8282\u70B9\u5750\u6807": {x: -181.50005, y: -72.000015}
  "\u5B50\u8282\u70B9":
  - {fileID: -562688680005327558}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u662F\u5426\u548C\u5546\u961F\u6210\u54584\u5BF9\u8BDD"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 0
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u9E7F\u773C\uFF0C\u9C7C\u5C3E\uFF0C\u5C71\u7F8A\u817F\uFF0C\u5E72\u9762\u5305\u2026\u2026"
--- !u!114 &3205896313162100493
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f716134a78f37df418d8eb84a3f5229d, type: 3}
  m_Name: ConditionCheckSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 0
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": f8e211e98bac8c447b0cdc02068de674
  "\u8282\u70B9\u5750\u6807": {x: 3.0998545, y: -298.8}
  "\u5B50\u8282\u70B9":
  - {fileID: 2804478208270175289}
  - {fileID: 9156018364789118897}
  "\u6761\u4EF6\u5408\u96C6": []
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
--- !u!114 &9156018364789118897
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bb6b11186ba7c841967e7fc50f7cc9b, type: 3}
  m_Name: DialogueSONode
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: "\u6539\u53D8\u5168\u5C40\u53D8\u91CF\u5217\u8868"
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Int32,
        mscorlib]], mscorlib
    - Name: comparer
      Entry: 9
      Data: 1
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  "\u6807\u9898": 
  "\u5F00\u542F\u622A\u65AD\u6761\u4EF6": 1
  "\u8C03\u8BD5": 0
  "\u8282\u70B9ID": c8571f0c6955b7b4aa68463d8bfa6633
  "\u8282\u70B9\u5750\u6807": {x: 450.99994, y: -45.000042}
  "\u5B50\u8282\u70B9":
  - {fileID: -2532644926316163608}
  "\u6761\u4EF6\u5408\u96C6":
  - "\u5E76\u5B58\u6761\u4EF6":
    - "\u6761\u4EF6\u7C7B\u578B": 0
      "\u6761\u4EF6\u540D": "\u662F\u5426\u548C\u5546\u961F\u6210\u54584\u5BF9\u8BDD"
      "\u8FD0\u7B97\u7B26": 0
      "\u503C": 1
      costItem: 0
  "\u8BBE\u7F6E\u5168\u5C40\u53D8\u91CF": 0
  "\u6539\u53D8\u5168\u5C40\u53D8\u91CF": 0
  "\u7ACB\u7ED8": {fileID: 21300000, guid: e7fde04ef1abc6043a1ec7f25290eec1, type: 3}
  "\u540D\u5B57": "\u4F0A\u68EE"
  "\u5BF9\u8BDD\u5185\u5BB9": "\u4ED6\u4FE9\u771F\u6162\u554A\u2026\u2026\u6211\u5DF2\u7ECF\u53EF\u4EE5\u9884\u60F3\u5230\u4E00\u4F1A\u827E\u4F2F\u7279\u5148\u751F\u4F1A\u600E\u4E48\u706B\u5C71\u7206\u53D1\u4E86\u3002"
