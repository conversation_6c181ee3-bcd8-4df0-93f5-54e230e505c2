%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d19bd4813e124894899e308a1513275e, type: 3}
  m_Name: JobParent_SO
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: jobParents
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[System.Collections.Generic.List`1[[System.String,
        mscorlib]], mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 38
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5192\u9669\u8005"
    - Name: $v
      Entry: 7
      Data: 2|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6E38\u8361\u8005"
    - Name: $v
      Entry: 7
      Data: 3|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u5251\u58EB"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u62A4\u536B"
    - Name: $v
      Entry: 7
      Data: 4|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u58EB\u5175"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u730E\u4EBA"
    - Name: $v
      Entry: 7
      Data: 5|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5251\u8C6A"
    - Name: $v
      Entry: 7
      Data: 6|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u5251\u58EB"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5251\u5723"
    - Name: $v
      Entry: 7
      Data: 7|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u5251\u8C6A"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u523A\u5BA2"
    - Name: $v
      Entry: 7
      Data: 8|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u6E38\u8361\u8005"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u72C2\u5F92"
    - Name: $v
      Entry: 7
      Data: 9|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u8D4C\u5F92"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u58EB\u5175"
    - Name: $v
      Entry: 7
      Data: 10|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u91CD\u88C5"
    - Name: $v
      Entry: 7
      Data: 11|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u62A4\u536B"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5148\u950B"
    - Name: $v
      Entry: 7
      Data: 12|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u58EB\u5175"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9A91\u58EB"
    - Name: $v
      Entry: 7
      Data: 13|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u5148\u950B"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5F13\u6B65\u5175"
    - Name: $v
      Entry: 7
      Data: 14|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 1
      Data: "\u58EB\u5175"
    - Name: 
      Entry: 1
      Data: "\u730E\u4EBA"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u91CE\u86EE\u4EBA"
    - Name: $v
      Entry: 7
      Data: 15|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u6218\u58EB"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u52C7\u8005"
    - Name: $v
      Entry: 7
      Data: 16|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u6218\u58EB"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u82F1\u96C4"
    - Name: $v
      Entry: 7
      Data: 17|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u52C7\u8005"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9B54\u6CD5\u5B66\u5F92"
    - Name: $v
      Entry: 7
      Data: 18|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5143\u7D20\u4F7F"
    - Name: $v
      Entry: 7
      Data: 19|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u9B54\u6CD5\u5B66\u5F92"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5143\u7D20\u5927\u5E08"
    - Name: $v
      Entry: 7
      Data: 20|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u5143\u7D20\u4F7F"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u672F\u58EB"
    - Name: $v
      Entry: 7
      Data: 21|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u9B54\u6CD5\u5B66\u5F92"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9690\u79D8\u4F7F\u8005"
    - Name: $v
      Entry: 7
      Data: 22|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9759\u8C27\u836F\u5E08"
    - Name: $v
      Entry: 7
      Data: 23|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u9690\u79D8\u4F7F\u8005"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u8D4C\u5F92"
    - Name: $v
      Entry: 7
      Data: 24|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5E7D\u5F71\u884C\u8005"
    - Name: $v
      Entry: 7
      Data: 25|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u9759\u8C27\u836F\u5E08"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9ED1\u591C\u4F7F\u8005"
    - Name: $v
      Entry: 7
      Data: 26|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u7EA2\u6708\u796D\u53F8"
    - Name: $v
      Entry: 7
      Data: 27|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6E38\u4FA0"
    - Name: $v
      Entry: 7
      Data: 28|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u730E\u4EBA"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u795E\u5C04\u624B"
    - Name: $v
      Entry: 7
      Data: 29|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u6E38\u4FA0"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u72C2\u6218\u58EB"
    - Name: $v
      Entry: 7
      Data: 30|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u91CE\u86EE\u4EBA"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u4EB2\u536B"
    - Name: $v
      Entry: 7
      Data: 31|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u5F13\u6B65\u5175"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6218\u58EB"
    - Name: $v
      Entry: 7
      Data: 32|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5251\u58EB"
    - Name: $v
      Entry: 7
      Data: 33|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u98CE\u96F7\u5251"
    - Name: $v
      Entry: 7
      Data: 34|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 1
      Data: "\u5143\u7D20\u4F7F"
    - Name: 
      Entry: 1
      Data: "\u9752\u4E91\u5251"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u82E6\u75DB\u5927\u5E08"
    - Name: $v
      Entry: 7
      Data: 35|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u672F\u58EB"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9752\u4E91\u5251"
    - Name: $v
      Entry: 7
      Data: 36|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u8840\u9B54"
    - Name: $v
      Entry: 7
      Data: 37|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u672F\u58EB"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u547D\u8FD0\u4E4B\u9AB0"
    - Name: $v
      Entry: 7
      Data: 38|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 1
      Data: "\u72C2\u5F92"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u7206\u7834\u72C2"
    - Name: $v
      Entry: 7
      Data: 39|System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 1
      Data: "\u72C2\u5F92"
    - Name: 
      Entry: 1
      Data: "\u6E38\u4FA0"
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
