---
description: 
globs: 战斗 AI 系统,战斗 AI
alwaysApply: false
---
# 战斗 AI 系统（行为树）

该目录包含战斗中的敌人 AI 系统的核心组件，基于 ScriptableObject 的行为树结构。

## 目录结构

- `AI_ActionTree.cs`: 定义主要的行为树资源 ([AI_ActionTree](mdc:Assets/Scripts/Battle/AI/AI_ActionTree.cs))，包含一组 `AI_ActionGroup`。
- `AI_ActionGroup.cs`: 定义行为树中的一组动作和条件 ([AI_ActionGroup](mdc:Assets/Scripts/Battle/AI/AI_ActionGroup.cs))。每组包含一组 `AI_NodeBase`。
- `AI_NodeBase.cs`: 所有节点（条件和动作）的抽象基类 ([AI_NodeBase](mdc:Assets/Scripts/Battle/AI/AI_NodeBase.cs))。
- `AI_ConditionInfo.cs`: 条件节点的抽象基类 ([AI_ConditionInfo](mdc:Assets/Scripts/Battle/AI/AI_ConditionInfo.cs))。具体条件继承自此类。
- `AI_ActionInfo.cs`: 动作节点的抽象基类 ([AI_ActionInfo](mdc:Assets/Scripts/Battle/AI/AI_ActionInfo.cs))。具体动作继承自此类。
- `ActionTreeManager.cs`: 附加到敌人的 MonoBehaviour 组件 ([ActionTreeManager](mdc:Assets/Scripts/Battle/AI/ActionTreeManager.cs))，负责管理和执行 `AI_ActionTree` 的运行时实例。
- `AICondition/`: 包含 `AI_ConditionInfo` 的具体实现（特定条件检查）。
- `AIAction/`: 包含 `AI_ActionInfo` 的具体实现（特定敌人动作）。

## 核心 API 摘要

### 数据结构（ScriptableObjects 和可序列化类）

- **`AI_ActionTree`**: 表示敌人类型的整个 AI 逻辑。包含一组 `AI_ActionGroup`。作为 ScriptableObject 资源创建。
- **`AI_ActionGroup`**: 一组条件和动作。由 `ActionTreeManager` 顺序评估。包含一组 `AI_NodeBase`。
- **`AI_NodeBase`**: 所有节点的基类。定义通用属性，如 `nodeName`、`nodeType`。`Run()` 方法是核心执行逻辑（由子类重写）。`CreateNewNode()` 用于运行时实例化。
- **`AI_ConditionInfo`**: 继承自 `AI_NodeBase`。表示条件检查。`Run()` 返回 true 如果条件满足，否则返回 false。具体条件实现特定检查（例如，检查生命值、距离）。
- **`AI_ActionInfo`**: 继承自 `AI_NodeBase`。表示敌人动作。
    - `PreRun()`: 在执行前调用，通常用于显示意图。
    - `CheckAvailable()`: 检查当前是否可以执行该动作。
    - `Run()` / `RunAsync()`: 执行动作逻辑（例如，使用技能、移动）。
    - `runContinue` (bool): 如果为 true，管理器在将此动作 *可能* 添加到潜在动作列表后继续评估后续节点。

### 运行时管理器

- **`ActionTreeManager` (MonoBehaviour)**:
    - **字段**:
        - `actionTree` (`AI_ActionTree`): 链接到定义 AI 的 SO 资源。
        - `runtimeActionTree` (`AI_ActionTree`): 用于运行时修改的实例化副本。
        - `getActionSituations` (List<`GetActionSituation`>): 定义 AI 何时应重新评估其意图。
        - `owner` (`Unit`): 引用此管理器控制的敌人单位。
        - `intensionAction` (`AI_ActionInfo`): 确定要执行的下一个动作（用于 UI）。
    - **关键方法**:
        - `Initialize()`: 创建树的运行时实例。
        - `StartGetAction()`: 由事件触发（定义在 `getActionSituations` 中），开始评估行为树以确定下一个动作。它遍历 `AI_ActionGroup`s。
        - `GoNextActionGroup()`: 当组完成评估时内部调用，移动到下一个组。
        - `OnActionGetSuccess()`: 当在组内成功识别出 `AI_ActionInfo` 时内部调用。
        - `CheckAllActions()`: 在所有组评估后调用。根据 `CheckAvailable()` 和 `runContinue` 过滤潜在动作以确定最终的 `intensionAction`。
        - `StartRunAction()`: 在敌人回合开始时调用以执行 `intensionAction`。
        - `RunCurrentAction()`: 执行 `intensionAction` 的 `Run()` 或 `RunAsync()` 方法。
    - **事件处理**: 监听各种 `BattleEventName` 事件以触发 `StartGetAction`。

### 工作流程概述

1.  在编辑器中创建一个 `AI_ActionTree` SO 资源，定义 `AI_ActionGroup`s 中的条件和动作序列。
2.  将 `ActionTreeManager` 组件附加到敌人预制件，链接到 `AI_ActionTree` 资源。
3.  在运行时，`ActionTreeManager.Initialize()` 创建一个运行时副本。
4.  基于 `getActionSituations`，特定战斗事件触发 `ActionTreeManager.StartGetAction()`。
5.  `StartGetAction` 遍历 `runtimeActionTree.actionGroups`。
6.  每个 `AI_ActionGroup` 顺序评估其 `AI_NodeBase` 列表：
    - 调用 `AI_ConditionInfo.Run()`。如果为 false，组停止，管理器移动到下一个组（`GoNextActionGroup`）。
    - 如果条件为 true，则继续下一个节点。
    - 当到达 `AI_ActionInfo` 时，`OnActionGetSuccess` 将其添加到临时列表。如果 `runContinue` 为 true，评估可能会在组内继续；否则，组可能会结束。
7.  在所有相关组评估后，`CheckAllActions` 根据 `CheckAvailable()` 和 `runContinue` 规则过滤收集的潜在动作以决定最终的 `intensionAction`。
8.  `intensionAction` 被存储并可能显示在 UI 中。
9.  当敌人的回合开始时，`StartRunAction()` 调用 `RunCurrentAction()`，执行 `intensionAction.Run()` 或 `RunAsync()`。