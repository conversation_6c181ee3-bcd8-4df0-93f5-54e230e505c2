%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!162 &1
EditorUserSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 4
  m_ConfigSettings:
    RecentlyUsedSceneGuid-0:
      value: 0755570706030c590b595c2144760e1114424e287d7c2760297f4e65b1e43239
      flags: 0
    RecentlyUsedSceneGuid-1:
      value: 515250075c0c595e5f5a5e71122159444e4e4a2f7a7d7f602f284d66b4b76661
      flags: 0
    RecentlyUsedSceneGuid-2:
      value: 5657075f54055e090f560d7747735a4446151b2e7b2b71627a7f4b64b1b5323b
      flags: 0
    RecentlyUsedSceneGuid-3:
      value: 5354070501545f0f595e5c2341715a4410154b7b292b70337e7d4865e3b23561
      flags: 0
    RecentlyUsedSceneGuid-4:
      value: 5457565300070d02590b5827147b0e441715412e747d236179704b35b2e5323e
      flags: 0
    RecentlyUsedSceneGuid-5:
      value: 020304025c54085e5d5f0875162609441515492b297d20337e7b1b30bae36068
      flags: 0
    RecentlyUsedSceneGuid-6:
      value: 545351035d035c025f0c0a7715740744154e192f7e7920657a7b1f61e4e3606b
      flags: 0
    RecentlyUsedSceneGuid-7:
      value: 54010d5f52000b0e580a5d724225064444164f2e7b7073602e7f1b67e3b5606a
      flags: 0
    RecentlyUsedSceneGuid-8:
      value: 5454555e52000b580c59582647200644434e1d787d2e25357e2c1e63e4e16539
      flags: 0
    RecentlyUsedSceneGuid-9:
      value: 515056055302500c5a0a0d2347745a44404f407a28712269782b1e67b6b46561
      flags: 0
    UnityEditor.ShaderGraph.Blackboard:
      value: 18135939215a0a5004000b0e15254b524c030a3f2964643d120d1230e9e93a3fd6e826abbd2e2d293c4ead313b08042de6030a0afa240c0d020be94c4bad5e435d8715fa32c70d15d11612dacc11fee5d3c5d1fe9ab1b588908fecb0f9cfddf1eff4e7a1b1eae482f0fda7e4e1928b86d888ed939b958797a7cf
      flags: 0
    UnityEditor.ShaderGraph.FloatingWindowsLayout2:
      value: 181344140043005e1a220d3b1f364b524c0c5a27130c293326201334cee5322ca0bd30e8eb293a707b0fd0180b3d0a36fc0d3d04e649500d1002ee0b5dbd1d2c27c00ad113cb1e10e41f1addc80993b9859884a69ae6d8f0d1cda9e8fbfefaf9f9dea3fdb9ade882f0fca9ffff8e85c9fef9bedec98493dcf8ca9389ef81879d899ac7cf9bdd89898cc3f090bf8d
      flags: 0
    UnityEditor.ShaderGraph.InspectorWindow:
      value: 18135939215a0a5004000b0e15254b524c1119263f2d6a722016393ce1eb3d36e5d339f9a5602b2e2c07a37e0901373ae01e0008f707250d171df81a53a542415f95548717f73713d91006c1c309d0effad0d2f9ddffa5828e91f0beb6fdd1cbfceba0b9f0b3bed8e8f5ade1ff8c978883d3f59e99959f89eacfcc
      flags: 0
    UnityEditor.ShaderGraph.ToggleSettings:
      value: 18135d1527590858060c032302276919051e1a26296a7c243f3c187fa0e92708f0e220e0e22d09352a0bed30017c5b2be01f0c47b40219221f1ded0b12eb1f0127cc0bcc18c41a5e910d0edbc85193e0dadadbf8e8f9e8ced7dba5e0b6aaacdbf4e5a0fca5bae582b7
      flags: 0
    vcSharedLogLevel:
      value: 0d5e400f0650
      flags: 0
  m_VCAutomaticAdd: 1
  m_VCDebugCom: 0
  m_VCDebugCmd: 0
  m_VCDebugOut: 0
  m_SemanticMergeMode: 2
  m_DesiredImportWorkerCount: 2
  m_StandbyImportWorkerCount: 2
  m_IdleImportWorkerShutdownDelay: 60000
  m_VCShowFailedCheckout: 1
  m_VCOverwriteFailedCheckoutAssets: 1
  m_VCProjectOverlayIcons: 1
  m_VCHierarchyOverlayIcons: 1
  m_VCOtherOverlayIcons: 1
  m_VCAllowAsyncUpdate: 1
  m_ArtifactGarbageCollection: 1
