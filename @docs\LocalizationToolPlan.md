# 游戏内文本本地化工具策划案

## 1. 目标

开发一个 Unity 编辑器工具，用于自动化提取游戏项目中需要本地化的文本内容，主要来源于 **`DialogueNodeTree` 中的对话** 以及 **各种游戏数据 ScriptableObjects (如卡牌、装备、武器、道具、刻印等)**，并生成一个基础的 CSV 文件 (`BaseLocalization.csv`)。该 CSV 文件将作为后续人工翻译或 AI 翻译的基础，最终生成支持多语言的 `.csv` 文件（如 `en.csv`, `jp.csv` 等）。

## 2. 核心流程

工具的核心工作流程如下：

1.  **开始:** 用户通过 Unity 编辑器菜单触发工具运行。
2.  **扫描资源:** 工具自动扫描项目指定目录下的特定 `ScriptableObject` 资源文件：
    *   `Assets/GameData/Dialogue` 目录下的所有 `DialogueNodeTree` 资源。
    *   `Assets/GameData/Invertory/Card` 目录下的所有 `CardData_SO` 资源。
    *   `Assets/GameData/Invertory/Equipment` 目录下的所有 `EquipmentData_SO` 资源。
    *   `Assets/GameData/Invertory/Weapon` 目录下的所有 `WeaponData_SO` 资源。
    *   `Assets/GameData/Invertory/Item` 目录下的所有 `ItemDataList_SO` 资源。
    *   `Assets/GameData/Invertory/Mark` 目录下的所有 `MarkData_SO` 资源。
    *(注意: 项目中实际路径为 Invertory，已在代码中修正)*
3.  **检查基础CSV:** 检查指定路径（`Assets/GameData/Localization/`）下是否存在 `BaseLocalization.csv` 文件。
4.  **分支处理:**
    *   **如果 `BaseLocalization.csv` 不存在:**
        *   初始化一个空的 `Dictionary<string, string>` 用于存储提取的数据。
        *   **提取节点文本 (Dialogue & Node Titles):** 遍历所有扫描到的 `DialogueNodeTree` 资源。
            *   获取 `DialogueNodeTree` 的 GUID (`TreeGUID`).
            *   遍历其包含的所有 `SONode` 节点。
                *   获取 `SONode` 的 GUID (`NodeGUID`) (已确认实现)。
                *   **提取标题 (所有 SONode):**
                    *   Key: `"NodeTitle_" + TreeGUID + "_" + NodeGUID` (例如: `NodeTitle_7a3b..._c8d1...`)
                    *   Value: `SONode.标题`
                    *   将此 Key-Value 对添加到数据字典 (如果 `SONode.标题` 不为空)。
                *   **提取对话内容 (仅 DialogueSONode):**
                    *   如果当前节点是 `DialogueSONode` 类型:
                        *   Key: `"Dialogue_" + TreeGUID + "_" + NodeGUID` (例如: `Dialogue_7a3b..._c8d1...`)
                        *   Value: `(DialogueSONode)node.对话内容`
                        *   将此 Key-Value 对添加到数据字典 (如果 `对话内容` 不为空)。
        *   **提取Card:** 遍历所有扫描到的 `CardData_SO` 资源。
            *   Key 1: `"Card_" + cardDetails.cardID + "_Name"` (例如: `Card_101_Name`)
            *   Value 1: `cardDetails.cardName`
            *   Key 2: `"Card_" + cardDetails.cardID + "_Desc"` (例如: `Card_101_Desc`)
            *   Value 2: `cardDetails.cardDescription`
            *   *(每个 CardData_SO 生成多行，每个需翻译字段一行)*
        *   **提取Equipment:** 遍历所有扫描到的 `EquipmentData_SO` 资源。
            *   Key 1: `"Equipment_" + equipmentDetails.equipmentID + "_Name"` (例如: `Equipment_205_Name`)
            *   Value 1: `equipmentDetails.name`
            *   Key 2: `"Equipment_" + equipmentDetails.equipmentID + "_Desc"` (例如: `Equipment_205_Desc`)
            *   Value 2: `equipmentDetails.description`
            *   Key 3: `"Equipment_" + equipmentDetails.equipmentID + "_Tips"` (例如: `Equipment_205_Tips`)
            *   Value 3: `equipmentDetails.tipsDescription`
            *   *(每个 EquipmentData_SO 生成多行)*
        *   **提取Weapon:** 遍历所有扫描到的 `WeaponData_SO` 资源。
            *   Key 1: `"Weapon_" + weaponDetail.weaponID + "_Name"` (例如: `Weapon_310_Name`)
            *   Value 1: `weaponDetail.weaponName`
            *   Key 2: `"Weapon_" + weaponDetail.weaponID + "_Desc"` (例如: `Weapon_310_Desc`)
            *   Value 2: `weaponDetail.description`
            *   Key 3: `"Weapon_" + weaponDetail.weaponID + "_Effect"` (例如: `Weapon_310_Effect`)
            *   Value 3: `weaponDetail.effectDescription`
            *   *(每个 WeaponData_SO 生成多行)*
        *   **提取Item:** 遍历所有扫描到的 `ItemDataList_SO` 资源。
            *(注意: ItemDataList_SO 实际代表单个物品，包含 item 字段)*
            *   Key 1: `"Item_" + item.itemID + "_Name"` (例如: `Item_401_Name`)
            *   Value 1: `item.itemName`
            *   Key 2: `"Item_" + item.itemID + "_Desc"` (例如: `Item_401_Desc`)
            *   Value 2: `item.itemDescription`
            *   *(每个 ItemDataList_SO 生成多行)*
        *   **提取Mark:** 遍历所有扫描到的 `MarkData_SO` 资源。
            *(注意: MarkData_SO 实际包含 markData 字段，内含 ID, name, description, tipsDescription)*
            *   Key 1: `"Mark_" + markData.ID + "_Name"` (例如: `Mark_502_Name`)
            *   Value 1: `markData.name`
            *   Key 2: `"Mark_" + markData.ID + "_Desc"` (例如: `Mark_502_Desc`)
            *   Value 2: `markData.description`
            *   Key 3: `"Mark_" + markData.ID + "_Tips"` (例如: `Mark_502_Tips`)
            *   Value 3: `markData.tipsDescription`
            *   *(每个 MarkData_SO 生成多行)*
        *   将所有提取的 `Key-Value` 对写入新的 `BaseLocalization.csv` 文件。
    *   **如果 `BaseLocalization.csv` 存在:**
        *   读取现有 `BaseLocalization.csv` 文件内容到 `csvData` (`Dictionary<string, string>`)。
        *   执行与"不存在"情况相同的扫描和提取过程，获取当前项目中所有的 `Key-Value` 对，存入 `currentData` (`Dictionary<string, string>`)。
        *   **智能更新 (日志报告):** 对比 `currentData` 与 `csvData`，识别**新增**、**修改**和**删除**条目，并在日志中报告数量。
            *   **新增:** `currentData` 中存在但 `csvData` 中不存在的 Key。
            *   **修改:** `currentData` 和 `csvData` 中都存在 Key，但 Value 不同。
            *   **删除:** `csvData` 中存在但 `currentData` 中不存在的 Key。
            *   **保留:** Key 和 Value 都相同。
        *   将 `currentData` **覆盖写入** `BaseLocalization.csv` 文件。
5.  **结束:** 工具执行完毕，输出操作结果信息（如创建了新文件、更新/新增/删除了多少条目等）。

## 3. CSV 文件结构 (`BaseLocalization.csv`)

*   **格式:** 标准 CSV 格式，使用逗号 `,` 作为分隔符。
*   **编码:** UTF-8 (确保支持多语言字符)。
*   **列定义:**
    *   **第一列:** `Key` (字符串)。唯一标识符，用于关联源数据。格式根据数据类型不同而变化。
    *   **第二列:** `Value` (字符串)。需要本地化的原始文本。

**示例:**

```csv
Key,Value
"Dialogue_7a3b..._c8d1...","你好，冒险者！"
"NodeTitle_7a3b..._c8d1...","开场白"
"NodeTitle_7a3b..._e2f9...","任务说明"
"Card_101_Name","火焰冲击"
"Card_101_Desc","对敌人造成火焰伤害。"
"Equipment_205_Name","铁质头盔"
"Equipment_205_Desc","提供基础物理防御。"
"Equipment_205_Tips","结实耐用。"
"Weapon_310_Name","新手长剑"
"Weapon_310_Desc","一把普通的剑。"
"Weapon_310_Effect","无特殊效果。"
"Item_401_Name","小瓶治疗药水"
"Item_401_Desc","恢复少量生命值。"
"Mark_502_Name","力量刻印"
"Mark_502_Desc","提升攻击力。"
"Mark_502_Tips","每次攻击时触发。"
```
*注意：当 Value 包含逗号时，整个 Value 需要用双引号 `"` 包裹。如果 Value 内部包含双引号，需要使用两个双引号 `""` 进行转义。CsvHelper 会自动处理。*

## 4. 实现思路

*   **工具类型:** 创建一个 C# 脚本 (`LocalizationGenerator.cs`)，使用静态方法搭配 `UnityEditor.MenuItem`。
*   **资源扫描:** 使用 `UnityEditor.AssetDatabase.FindAssets` 查找指定目录下特定类型的资源。需要使用正确的搜索路径（包括 `Invertory` 拼写）。
*   **数据提取:** 实现针对不同 `ScriptableObject` 类型的提取逻辑。注意 `ItemDataList_SO` 和 `MarkData_SO` 的实际结构。
*   **Key 生成:** 根据类型、ID (`节点ID` 或 `itemID` 等) 和字段名称（或代表字段的后缀，如 `_Name`, `_Desc`）生成唯一的字符串 Key。
*   **Value:** 直接使用字段的原始文本作为 Value。
*   **GUID 获取 (DialogueNode):**
    *   `DialogueNodeTree` GUID: `AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(treeAsset))`。
    *   `DialogueSONode` GUID: 使用 `SONode.节点ID` 属性 (已确认)。
*   **CSV 读写:** 使用 `CsvHelper` 库简化 CSV 文件的读写操作。确保读写时指定 `Encoding.UTF8`。每次生成时，**覆盖写入** `BaseLocalization.csv`。
*   **用户界面:** 在 Unity 编辑器顶部菜单栏添加选项触发工具。
*   **路径配置:** `BaseLocalization.csv` 的路径固定为 `Assets/GameData/Localization/`。

## 5. ToDo List (更新于 [当前日期])

-   [x] 创建 Editor 脚本 `LocalizationGenerator.cs`。
-   [x] **(Dialogue Tree)** 实现扫描 `Assets/GameData/Dialogue` 目录下所有 `DialogueNodeTree` 资源的功能。
-   [x] **(Dialogue Tree)** 确定 `DialogueNodeTree` 存储其节点的方式 (`节点列表`)，并实现遍历所有 `SONode` 节点。
-   [x] **(Dialogue Tree)** 在 `SONode` (确认 `节点ID` 属性存在且唯一) 中添加/确认 `guid` 字段并确保其在编辑器中能自动生成和保存。
-   [x] **(Dialogue Tree - Title)** 实现获取 `DialogueNodeTree` GUID 和 `SONode` GUID (`节点ID`) 并组合成节点标题 `Key` (`"NodeTitle_" + TreeGUID + "_" + NodeGUID`) 的逻辑。
-   [x] **(Dialogue Tree - Title)** 实现从 `SONode` 提取 `标题` 作为 `Value` 的逻辑 (检查非空)。
-   [x] **(Dialogue Tree - Content)** 实现获取 `DialogueNodeTree` GUID 和 `DialogueSONode` GUID (`节点ID`) 并组合成对话内容 `Key` (`"Dialogue_" + TreeGUID + "_" + NodeGUID`) 的逻辑。
-   [x] **(Dialogue Tree - Content)** 实现从 `DialogueSONode` 提取 `对话内容` 作为 `Value` 的逻辑 (检查非空，类型转换)。
-   [x] **(Card)** 实现扫描 `Assets/GameData/Invertory/Card` 目录下所有 `CardData_SO` 资源的功能。
-   [x] **(Card)** 实现为 `cardName` 和 `cardDescription` 分别生成 `Key` (带 `_Name`, `_Desc` 后缀) 的逻辑。
-   [x] **(Card)** 实现提取 `cardName` 和 `cardDescription` 作为各自独立 `Value` 的逻辑。
-   [x] **(Equipment)** 实现扫描 `Assets/GameData/Invertory/Equipment` 目录下所有 `EquipmentData_SO` 资源的功能。
-   [x] **(Equipment)** 实现为 `name`, `description`, `tipsDescription` 分别生成 `Key` (带 `_Name`, `_Desc`, `_Tips` 后缀) 的逻辑。
-   [x] **(Equipment)** 实现提取 `name`, `description`, `tipsDescription` 作为各自独立 `Value` 的逻辑。
-   [x] **(Weapon)** 实现扫描 `Assets/GameData/Invertory/Weapon` 目录下所有 `WeaponData_SO` 资源的功能。
-   [x] **(Weapon)** 实现为 `weaponName`, `description`, `effectDescription` 分别生成 `Key` (带 `_Name`, `_Desc`, `_Effect` 后缀) 的逻辑。
-   [x] **(Weapon)** 实现提取 `weaponName`, `description`, `effectDescription` 作为各自独立 `Value` 的逻辑。
-   [x] **(Item)** 实现扫描 `Assets/GameData/Invertory/Item` 目录下所有 `ItemDataList_SO` 资源的功能。
-   [x] **(Item)** 实现为 `itemName`, `itemDescription` 分别生成 `Key` (带 `_Name`, `_Desc` 后缀) 的逻辑 (适配 `ItemDataList_SO` 代表单物品结构)。
-   [x] **(Item)** 实现提取 `itemName`, `itemDescription` 作为各自独立 `Value` 的逻辑 (适配 `ItemDataList_SO` 代表单物品结构)。
-   [x] **(Mark)** 实现扫描 `Assets/GameData/Invertory/Mark` 目录下所有 `MarkData_SO` 资源的功能。
-   [x] **(Mark)** 实现为 `name`, `description`, `tipsDescription` 分别生成 `Key` (带 `_Name`, `_Desc`, `_Tips` 后缀) 的逻辑 (适配 `MarkData_SO` 包含 `markData` 字段结构)。
-   [x] **(Mark)** 实现提取 `name`, `description`, `tipsDescription` 作为各自独立 `Value` 的逻辑 (适配 `MarkData_SO` 包含 `markData` 字段结构)。
-   [x] 实现将所有来源提取的细粒度 `Key-Value` 对合并到一个 `Dictionary<string, string>` (`currentLocalizationData`) 的逻辑。
-   [x] 实现检查 `BaseLocalization.csv` 文件是否存在的功能。
-   [x] **(CSV)** 使用 `CsvHelper` 实现创建新的 `BaseLocalization.csv` 文件并写入表头和 `currentLocalizationData` 的功能 (指定 UTF-8 编码)。
-   [x] **(CSV)** 使用 `CsvHelper` 实现读取现有 `BaseLocalization.csv` 文件内容到 `Dictionary<string, string>` (`csvData`) 的功能 (指定 UTF-8 编码)。
-   [x] 实现对比 `currentLocalizationData` 和 `csvData`，识别**新增**、**修改**和**删除**条目并在日志中报告的逻辑。
-   [x] **(CSV)** 使用 `CsvHelper` 实现将 `currentLocalizationData` **覆盖写入** `BaseLocalization.csv` 的功能 (指定 UTF-8 编码)。
-   [x] 在 Unity Editor 中添加菜单项以触发脚本执行。
-   [x] 添加必要的 `Debug.Log` 或 `EditorUtility.DisplayDialog` 来向用户反馈操作结果（包括各类型数据的增删改数量）。
-   [x] 确保路径 `Assets/GameData/Localization/` 在写入前存在。
-   [x] 编写简单的使用说明或注释 (通过本次对话完成)。

## 6. 运行时本地化读取方案

本方案旨在实现在游戏运行时动态加载和显示翻译文本的功能。

1.  **`LocalizationManager` (核心管理器):**
    *   **类型:** 创建一个全局单例脚本 `LocalizationManager.cs`。
    *   **职责:**
        *   **加载语言数据:** 负责在游戏启动或切换语言时，从 `StreamingAssets/Localization/` 目录加载对应的 `.csv` 文件（如 `en.csv`, `jp.csv`）。同时必须加载 `BaseLocalization.csv` 作为后备（基础）语言。
        *   **存储数据:** 将加载的 CSV 数据解析并存储到 `Dictionary<string, string>` 中。需要至少两个字典：一个用于当前选定语言 (`currentLanguageData`)，一个用于基础语言 (`baseLanguageData`)。
        *   **管理当前语言:** 维护当前选择的语言标识（如 `SystemLanguage` 或语言代码字符串）。提供 `SetLanguage(string langCode)` 方法来切换语言。
        *   **提供翻译接口:** 提供核心方法 `public string GetLocalizedString(string key)`。此方法根据 `key` 返回当前语言的翻译文本。
        *   **后备逻辑:** 如果在 `currentLanguageData` 中找不到 `key`，则在 `baseLanguageData` 中查找。若都找不到，返回明确的错误提示（如 `"[MISSING_KEY: " + key + "]"`）或 `key` 本身。
        *   **语言切换通知:** 使用 C# 事件 (`event Action OnLanguageChanged;`)，在语言成功切换后触发，通知相关 UI 组件更新文本。

2.  **CSV 文件存储:**
    *   **位置:** 所有翻译文件（包括 `BaseLocalization.csv` 及各语言 `xx.csv`）统一存放在 `Assets/StreamingAssets/Localization/` 目录下。
    *   **加载方式:** 使用 `UnityEngine.Networking.UnityWebRequest` 或 `System.IO.File`（注意平台兼容性）读取 `StreamingAssets` 中的 CSV 文件。

3.  **`LocalizedText` 组件:**
    *   **类型:** 创建 `MonoBehaviour` 脚本 `LocalizedText.cs`。
    *   **用途:** 附加到需要显示本地化文本的 UI 元素（如 `UnityEngine.UI.Text` 或 `TMPro.TextMeshProUGUI`）所在的 GameObject 上。
    *   **职责:**
        *   **存储 Key:** 提供 Inspector 字段 `localizationKey` 以设置对应的本地化 Key。
        *   **获取目标:** 自动获取关联的 `Text` 或 `TextMeshProUGUI` 组件。
        *   **初始化:** 在 `Start()` 时调用 `LocalizationManager.Instance.GetLocalizedString` 设置初始文本。
        *   **事件订阅:** 在 `OnEnable()` / `OnDisable()` 中订阅/取消订阅 `LocalizationManager.Instance.OnLanguageChanged` 事件。
        *   **更新文本:** 在事件处理方法中，重新获取并设置新语言的文本。

4.  **语言切换触发器:**
    *   提供 UI 机制（如下拉菜单、按钮）允许玩家或游戏逻辑调用 `LocalizationManager.Instance.SetLanguage()`。

## 7. 开发备注

*   **路径拼写错误:** 在开发过程中发现，项目中存储卡牌、装备、武器、物品、刻印数据的文件夹实际名称为 `Assets/GameData/Invertory/` (拼写错误)，而非策划案最初设想的 `Assets/GameData/Inventory/`。已修改 `LocalizationGenerator.cs` 中的路径常量以匹配实际路径，避免了重命名项目文件夹可能带来的风险。
*   **SO 结构差异:**
    *   `ItemDataList_SO` 脚本实际代表单个物品数据，其需要本地化的字段 `itemName` 和 `itemDescription` 存储在名为 `item` 的 `ItemDetails` 类型的公共字段中。
    *   `MarkData_SO` 脚本实际需要本地化的字段 `name`, `description`, `tipsDescription` 以及 `ID` 存储在名为 `markData` 的 `Mark` 类型的公共字段中。
    *   代码中的提取逻辑已根据这些实际结构进行了调整。
*   **CSV 更新逻辑:** 当前实现采用**覆盖写入** `BaseLocalization.csv` 的方式。每次运行工具，都会用当前项目中能找到的所有源文本重新生成文件。这意味着如果源资源被删除，对应的 Key 也会从基础 CSV 中消失。

## 8. 后续步骤

1.  **完善CSV更新逻辑:** 确认使用"覆盖写入+明确报告删除列表"的策略：
    * 维持当前的覆盖写入策略，确保`BaseLocalization.csv`文件始终精确反映项目当前状态。
    * 增强日志报告功能，当检测到删除的Key时，详细记录这些Key，方便翻译人员参考。
    * 考虑添加一个额外功能，将删除的Key列表导出到单独文件（如`RemovedKeys.txt`），方便本地化管理人员清理各语言文件中不再需要的条目。

2.  **工具改进 (可选):** 考虑是否需要将搜索路径、输出路径等做成可配置项。

3.  **实施运行时本地化系统:** 根据策划案第 6 节，开始创建 `LocalizationManager`、`LocalizedText` 组件等，实现在游戏运行时加载和显示翻译文本的功能。


</rewritten_file> 