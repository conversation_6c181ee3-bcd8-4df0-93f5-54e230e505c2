%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97ff5d9a6eab6f5419f435787a49121d, type: 3}
  m_Name: ConfigData_SO
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: jobNodesPosition
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UnityEngine.Vector3,
        UnityEngine.CoreModule]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 38
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5192\u9669\u8005"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.94042969
    - Name: 
      Entry: 4
      Data: -0.7558594
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6E38\u8361\u8005"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -645.8779
    - Name: 
      Entry: 4
      Data: -195.062866
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u62A4\u536B"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 672.3125
    - Name: 
      Entry: 4
      Data: 84.57596
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u730E\u4EBA"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 244.030045
    - Name: 
      Entry: 4
      Data: -217.69899
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u8D4C\u5F92"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 14.0510092
    - Name: 
      Entry: 4
      Data: -365.406281
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5251\u5723"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -1020.63336
    - Name: 
      Entry: 4
      Data: 121.8444
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u523A\u5BA2"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -988.386841
    - Name: 
      Entry: 4
      Data: -308.243958
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u72C2\u5F92"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 103.53846
    - Name: 
      Entry: 4
      Data: -661.832947
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u58EB\u5175"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 332.8496
    - Name: 
      Entry: 4
      Data: 70.84448
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u91CD\u88C5"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 1015.19043
    - Name: 
      Entry: 4
      Data: 96.0128
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5148\u950B"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 601.550354
    - Name: 
      Entry: 4
      Data: 346.9854
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9A91\u58EB"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 880.959656
    - Name: 
      Entry: 4
      Data: 495.909149
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5F13\u6B65\u5175"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 633.9831
    - Name: 
      Entry: 4
      Data: -204.369446
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u98CE\u96F7\u5251"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -894.876465
    - Name: 
      Entry: 4
      Data: 516.7208
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u91CE\u86EE\u4EBA"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 169.026276
    - Name: 
      Entry: 4
      Data: 695.2955
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u52C7\u8005"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 390.358337
    - Name: 
      Entry: 4
      Data: 574.569
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u82F1\u96C4"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 596.599548
    - Name: 
      Entry: 4
      Data: 816.0221
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9B54\u6CD5\u5B66\u5F92"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -128.569473
    - Name: 
      Entry: 4
      Data: 283.3769
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5143\u7D20\u4F7F"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -432.0913
    - Name: 
      Entry: 4
      Data: 524.2663
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5143\u7D20\u5927\u5E08"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -643.3628
    - Name: 
      Entry: 4
      Data: 780.810242
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u672F\u58EB"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -107.638626
    - Name: 
      Entry: 4
      Data: 675.174438
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u82E6\u75DB\u5927\u5E08"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -323.94046
    - Name: 
      Entry: 4
      Data: 984.536255
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9690\u79D8\u4F7F\u8005"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -226.30043
    - Name: 
      Entry: 4
      Data: -227.759689
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9759\u8C27\u836F\u5E08"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -497.484833
    - Name: 
      Entry: 4
      Data: -449.091766
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9752\u4E91\u5251"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -618.211365
    - Name: 
      Entry: 4
      Data: 312.9947
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5E7D\u5F71\u884C\u8005"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -827.4181
    - Name: 
      Entry: 4
      Data: -627.6664
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u9ED1\u591C\u4F7F\u8005"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -594.033142
    - Name: 
      Entry: 4
      Data: -829.3203
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u7EA2\u6708\u796D\u53F8"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -301.95108
    - Name: 
      Entry: 4
      Data: -949.8702
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6E38\u4FA0"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 489.452576
    - Name: 
      Entry: 4
      Data: -468.875977
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u8840\u9B54"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -24.2379017
    - Name: 
      Entry: 4
      Data: 1022.90204
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u547D\u8FD0\u4E4B\u9AB0"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 22.44058
    - Name: 
      Entry: 4
      Data: -1011.39282
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u72C2\u6218\u58EB"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 304.843719
    - Name: 
      Entry: 4
      Data: 969.445435
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u7206\u7834\u72C2"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 458.691223
    - Name: 
      Entry: 4
      Data: -919.108948
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u795E\u5C04\u624B"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 864.1807
    - Name: 
      Entry: 4
      Data: -583.5315
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u4EB2\u536B"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 990.0221
    - Name: 
      Entry: 4
      Data: -250.750519
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u6218\u58EB"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 151.078445
    - Name: 
      Entry: 4
      Data: 280.5805
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5251\u8C6A"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -681.089844
    - Name: 
      Entry: 4
      Data: 71.54156
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: "\u5251\u58EB"
    - Name: $v
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: -313.137024
    - Name: 
      Entry: 4
      Data: 9.322008
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: mapNodesPosition
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UnityEngine.Vector3,
        UnityEngine.CoreModule]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 3|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  weaponUpdateMat:
  - m_itemDataList_SO: {fileID: 11400000, guid: c9b03f84056018841a8e05af312867fa,
      type: 2}
    itemID: 15005
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: c9b03f84056018841a8e05af312867fa,
      type: 2}
    itemID: 15005
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: c9b03f84056018841a8e05af312867fa,
      type: 2}
    itemID: 15005
    ShopListID: 0
    Count: 6
  - m_itemDataList_SO: {fileID: 11400000, guid: 36945e59ba39d574fbb623b0ce04b406,
      type: 2}
    itemID: 15011
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 36945e59ba39d574fbb623b0ce04b406,
      type: 2}
    itemID: 15011
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: 36945e59ba39d574fbb623b0ce04b406,
      type: 2}
    itemID: 15011
    ShopListID: 0
    Count: 6
  - m_itemDataList_SO: {fileID: 11400000, guid: 794a57b51a3dd0d4392761c61fc25b5d,
      type: 2}
    itemID: 15012
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 794a57b51a3dd0d4392761c61fc25b5d,
      type: 2}
    itemID: 15012
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: 794a57b51a3dd0d4392761c61fc25b5d,
      type: 2}
    itemID: 15012
    ShopListID: 0
    Count: 6
  - m_itemDataList_SO: {fileID: 11400000, guid: 17bd121df5a532049934d0eae88ae917,
      type: 2}
    itemID: 15013
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 17bd121df5a532049934d0eae88ae917,
      type: 2}
    itemID: 15013
    ShopListID: 0
    Count: 4
  healTimesUpdateMat:
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 2
  healPowerUpdateMat:
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 3
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 3
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 3
  - m_itemDataList_SO: {fileID: 11400000, guid: 872b788d55005c948ab8108a18b7f580,
      type: 2}
    itemID: 100104
    ShopListID: 0
    Count: 1
  - m_itemDataList_SO: {fileID: 11400000, guid: 872b788d55005c948ab8108a18b7f580,
      type: 2}
    itemID: 100104
    ShopListID: 0
    Count: 2
  markSpaceUpdateMat:
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: 872b788d55005c948ab8108a18b7f580,
      type: 2}
    itemID: 100104
    ShopListID: 0
    Count: 2
  InherentCardSlotUpdateMat:
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: 872b788d55005c948ab8108a18b7f580,
      type: 2}
    itemID: 100104
    ShopListID: 0
    Count: 2
  perfumeSpaceUpdateMat:
  - m_itemDataList_SO: {fileID: 11400000, guid: 37a1783be0a16d448bff531ea7e307c0,
      type: 2}
    itemID: 10010
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: 326119fff4420204ca81b2f3f191ba72,
      type: 2}
    itemID: 100102
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 2
  - m_itemDataList_SO: {fileID: 11400000, guid: f7f59420419bf5f4bbf2de2326fd3e2c,
      type: 2}
    itemID: 100103
    ShopListID: 0
    Count: 4
  - m_itemDataList_SO: {fileID: 11400000, guid: 872b788d55005c948ab8108a18b7f580,
      type: 2}
    itemID: 100104
    ShopListID: 0
    Count: 2
