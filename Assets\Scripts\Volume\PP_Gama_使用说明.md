# PP_Gama 脚本使用说明

## 概述
`PP_Gama` 是一个用于控制 Unity URP 后处理中 Lift Gamma Gain 效果的脚本。它提供了对图像亮度、对比度和颜色调整的精确控制。

## 主要功能

### 1. Lift（阴影/暗部控制）
- **作用**：控制图像的暗部区域
- **参数**：
  - `LiftR/G/B`：分别控制红、绿、蓝通道的阴影
  - `LiftIntensity`：整体阴影强度
- **效果**：提高 Lift 值会让暗部变亮，降低会让暗部更暗

### 2. Gamma（中间调控制）
- **作用**：控制图像的中间调区域
- **参数**：
  - `GammaR/G/B`：分别控制红、绿、蓝通道的中间调
  - `GammaIntensity`：整体中间调强度
- **效果**：调整整体图像的亮度，影响中等亮度的像素

### 3. Gain（高光/亮部控制）
- **作用**：控制图像的亮部区域
- **参数**：
  - `GainR/G/B`：分别控制红、绿、蓝通道的高光
  - `GainIntensity`：整体高光强度
- **效果**：调整图像的最亮部分

## 使用方法

### 基础设置
1. 将 `PP_Gama` 脚本添加到带有 Volume 组件的 GameObject 上
2. 确保 Volume 组件有一个 VolumeProfile
3. 脚本会自动添加 LiftGammaGain 后处理效果到 Profile 中

### 通过代码控制
```csharp
// 获取 PP_Gama 实例
PP_Gama gamaController = PP_Gama.instance;

// 启用效果
gamaController.IsActive = true;

// 设置整体 gamma
gamaController.SetOverallGamma(1.2f);

// 设置整体 gain
gamaController.SetOverallGain(1.1f);

// 独立控制颜色通道
gamaController.GammaR = 1.1f;
gamaController.GammaG = 1.0f;
gamaController.GammaB = 0.9f;

// 重置所有参数
gamaController.ResetAll();
```

### 便捷方法
- `ResetAll()`：重置所有参数到默认值
- `SetOverallGamma(float)`：设置整体 gamma 值
- `SetOverallGain(float)`：设置整体 gain 值
- `SetOverallLift(float)`：设置整体 lift 值
- `SetLiftGammaGain(Vector4, Vector4, Vector4)`：一次性设置所有参数

## 常用预设效果

### 明亮效果
```csharp
gamaController.SetOverallGamma(1.2f);
gamaController.SetOverallGain(1.1f);
gamaController.SetOverallLift(1.0f);
```

### 暗沉效果
```csharp
gamaController.SetOverallGamma(0.8f);
gamaController.SetOverallGain(0.9f);
gamaController.SetOverallLift(0.95f);
```

### 高对比度效果
```csharp
gamaController.SetOverallLift(0.9f);   // 降低阴影
gamaController.SetOverallGamma(1.0f);  // 保持中间调
gamaController.SetOverallGain(1.2f);   // 提高高光
```

### 电影风格效果
```csharp
gamaController.SetLiftGammaGain(
    new Vector4(1.05f, 1.05f, 1.05f, 0.1f), // 稍微提亮阴影
    new Vector4(0.95f, 0.95f, 0.95f, 0f),   // 稍微降低中间调
    new Vector4(1.0f, 1.0f, 1.0f, 0f)       // 保持高光不变
);
```

## 测试脚本
使用 `PP_GamaTest` 脚本可以快速测试各种效果：

### 键盘快捷键
- `1`：明亮预设
- `2`：暗沉预设
- `3`：高对比度预设
- `4`：电影风格预设
- `0`：重置为默认

### Inspector 控制
在 Inspector 中可以直接点击按钮应用各种预设，或者使用滑块进行实时调整。

## 注意事项

1. **性能**：Lift Gamma Gain 是一个轻量级的后处理效果，对性能影响较小
2. **值范围**：
   - Lift/Gamma/Gain RGB 值通常在 0.5-2.0 之间
   - 强度值通常在 -1.0 到 1.0 之间
3. **视觉效果**：过度调整可能导致图像失真，建议进行适度调整
4. **兼容性**：需要 Unity URP 渲染管线支持

## 扩展功能
脚本支持单例模式，可以通过 `PP_Gama.instance` 在任何地方访问。这使得在游戏中动态调整图像效果变得非常方便。

## 故障排除

### 效果不生效
1. 检查 Volume 组件是否正确配置
2. 确认 `IsActive` 属性为 true
3. 检查 VolumeProfile 是否包含 LiftGammaGain 效果

### 参数调整无效果
1. 确认参数的 overrideState 为 true（脚本会自动设置）
2. 检查参数值是否在合理范围内
3. 确认相机在 Volume 的影响范围内
