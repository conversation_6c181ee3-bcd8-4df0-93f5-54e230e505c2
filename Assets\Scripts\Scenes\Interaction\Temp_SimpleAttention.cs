public class Temp_SimpleAttention : Event_Base
{
    public string simpleLog;
    public float duration = 5.0f;
    public bool translate = false;
    public string localKey;

    public void Attention()
    {
        if(LocalizationManager.Instance.IsBaseLanguage() != true && translate)
        {
            string simpleLogStr = localKey;
            UIManager.instance.SimpleAttention(LocalizationManager.Instance.GetLocalizedString(simpleLogStr),duration);
        }
        else
        UIManager.instance.SimpleAttention(simpleLog,duration);
    }

    public override void DoEvent()
    {
        Attention();
    }
}
